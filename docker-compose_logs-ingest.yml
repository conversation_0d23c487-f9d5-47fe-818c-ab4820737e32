version: '2'

services:
  geoipupdate:
    build: ./geoipupdate
    image: ${BASE_NAME}/geoipupdate
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_geoipupdate
    volumes:
      - ${GEOIP_LOG_DIR}:/var/log/geoipupdate
      - ./geoipupdate/etc/${CONFIG_NAME}/crontab:/etc/geoipupdate/crontab:ro
      - ./geoipupdate/bin/entry.sh:/usr/local/bin/entry.sh:ro
      - ./geoipupdate/etc/${CONFIG_NAME}/GeoIP.conf:/usr/local/etc/GeoIP.conf:ro
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP
    restart: unless-stopped
  mailer:
    build: ./mailer
    hostname: mail
    domainname: ${DOMAIN_NAME}
    image: ${BASE_NAME}/mailer
    container_name: ${BASE_NAME}_mailer
    volumes:
      - ${MAILER_DATA_DIR}:/var/mail
      - ${MAILER_STATE_DIR}:/var/mail-state
      - ${MAILER_CONFIG_DIR}:/tmp/docker-mailserver
      - ./mailer/bin/entry.sh:/usr/local/bin/entry.sh:ro
    restart: unless-stopped
  saver:
    build: ./saver
    image: ${BASE_NAME}/saver
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_saver
    volumes:
      - ${SAVER_RSYSLOG_LOG_DIR}:/var/log/rsyslog
      - ${SAVER_DRUID_LOG_DIR}:/var/log/druid
      - ${UPDATE_LOG_DIR}:/data/log/update
      - ${ARCHIVE_LOG_DIR}:/data/log/archive
      - ${ARCHIVE_DB_DIR}:/data/druid/sql
      - ./saver/src:/home/<USER>/loguploaderpackage:ro
      - ./saver/etc/${CONFIG_NAME}/s3cfg/.s3cfg:/home/<USER>/.s3cfg:ro
      - ./saver/etc/${CONFIG_NAME}:/etc/saver:ro
      - ./saver/bin:/usr/local/bin:ro
    links:
      - mailer:mailer-provider
    extra_hosts:
      - "db-provider:***************"
    restart: unless-stopped
  zookeeper:
    build: ./zookeeper
    image: ${BASE_NAME}/zookeeper
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_zookeeper
    volumes:
      - ${ZOOKEEPER_LOG_DIR}:/var/log
      - ./zookeeper/etc/${CONFIG_NAME}:/etc/zookeeper:ro
      - ./zookeeper/bin:/usr/local/bin:ro
      - ${ZOOKEEPER_DATA_DIR}:/data
      - ${ZOOKEEPER_TMP_DIR}:/tmp
    ports:
      - 2181:2181
    restart: unless-stopped
  kafka:
    build: ./kafka
    image: ${BASE_NAME}/kafka
    env_file: docker-compose.env
    environment:
      - FILTER_NUMBER=3
    container_name: ${BASE_NAME}_kafka
    hostname: kafka-server
    volumes:
      - ${KAFKA_LOG_DIR}:/var/log
      - ./kafka/etc/${CONFIG_NAME}:/etc/kafka:ro
      - ./kafka/bin:/usr/local/bin:ro
      - ${KAFKA_DATA_DIR}:/data
      - ${KAFKA_TMP_DIR}:/tmp
    links:
      - zookeeper:zookeeper-server
    ports:
      - 9092:9092
    restart: unless-stopped
  rsyslog:
    build: ./rsyslog
    image: ${BASE_NAME}/rsyslog
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_rsyslog
    hostname: ${BASE_NAME}-rsyslog
    volumes:
      - ${RSYSLOG_LOG_DIR}:/var/log
      - ./rsyslog/etc/${CONFIG_NAME}/rsyslog.d:/etc/rsyslog.d:ro
      - ./rsyslog/etc/${CONFIG_NAME}/rsyslog.conf:/etc/rsyslog.conf:ro
      - ${UPDATE_LOG_DIR}:/data/log/update
      - ${SPOOL_LOG_DIR}:/var/spool/rsyslog
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
      - ./rsyslog/bin/entry.sh:/usr/local/bin/entry.sh:ro
    links:
      - zookeeper:zookeeper-server
      - kafka:kafka-server
    ports:
      - 34510-34540:34510-34540
    restart: unless-stopped
  filter1:
    build: ./filter
    image: ${BASE_NAME}/filter
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
    container_name: ${BASE_NAME}_filter1
    volumes:
      - ./filter/log/filter1:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter:ro
      - ./filter/etc/${CONFIG_NAME}:/etc/filter:ro
      - ./filter/bin:/usr/local/bin:ro
      - ${UA_DATABASE_DIR}/filter1:/usr/local/share/uasparser3/database
    links:
      - zookeeper:zookeeper-server
      - kafka:kafka-server
      - mailer:mailer-server
    restart: unless-stopped
  filter2:
    build: ./filter
    image: ${BASE_NAME}/filter
    env_file: docker-compose.env
    environment:
      - FILTER_ID=2
    container_name: ${BASE_NAME}_filter2
    volumes:
      - ./filter/log/filter2:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter:ro
      - ./filter/etc/${CONFIG_NAME}:/etc/filter:ro
      - ./filter/bin:/usr/local/bin:ro
      - ${UA_DATABASE_DIR}/filter2:/usr/local/share/uasparser3/database
    links:
      - zookeeper:zookeeper-server
      - kafka:kafka-server
      - mailer:mailer-server
    restart: unless-stopped
  filter3:
    build: ./filter
    image: ${BASE_NAME}/filter
    env_file: docker-compose.env
    environment:
      - FILTER_ID=3
    container_name: ${BASE_NAME}_filter3
    volumes:
      - ./filter/log/filter3:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter:ro
      - ./filter/etc/${CONFIG_NAME}:/etc/filter:ro
      - ./filter/bin:/usr/local/bin:ro
      - ${UA_DATABASE_DIR}/filter3:/usr/local/share/uasparser3/database
    links:
      - zookeeper:zookeeper-server
      - kafka:kafka-server
      - mailer:mailer-server
    restart: unless-stopped
  historical:
    build: ./druid-historical
    image: ${BASE_NAME}/historical
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_historical
    hostname: druid-historical
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-historical/etc/${CONFIG_NAME}:/etc/druid/historical:ro
      - ${HISTORICAL_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/historical:/data/druid/tmp
      - ${INDEX_CACHE_DIR}:/data/druid/indexCache
    links:
      - zookeeper:zookeeper-server
    extra_hosts:
      - "db-provider:***************"
    ports:
      - 8083:8083
    restart: unless-stopped
  realtime:
    build: ./druid-realtime
    image: ${BASE_NAME}/realtime
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_realtime
    hostname: druid-realtime
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-realtime/etc/${CONFIG_NAME}:/etc/druid/realtime:ro
      - ${REALTIME_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/realtime:/data/druid/tmp
      - ${BASE_PERSIST_DIR}:/data/druid/basePersist
    links:
      - zookeeper:zookeeper-server
      - kafka:kafka-server
    ports:
      - 8084:8084
    extra_hosts:
      - "db-provider:***************"
      - "druid-coordinator:***************"
    restart: unless-stopped