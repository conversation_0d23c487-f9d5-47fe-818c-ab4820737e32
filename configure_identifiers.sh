#!/bin/bash -e
BASE=$(dirname "$0")
# shellcheck source=./.env
source "$BASE/.env"
if [ -z "${CONFIG_NAME}" ]; then
    echo "Need to set CONFIG_NAME in .env file."
    exit 1
fi
################################### Maxminddb ##################################
if [ -d "$BASE/geoipupdate/etc/${CONFIG_NAME}" ]; then
    CONFIG_FILE="$BASE/geoipupdate/etc/$CONFIG_NAME/GeoIP.conf"
    if [ ! -f "$CONFIG_FILE" ]
    then
        cp "${CONFIG_FILE}.dist" "${CONFIG_FILE}"
    fi
    echo Empty value means unchanged.
    # Request user-id
    echo -n Maxminddb user-id:
    read -rs user_id
    echo
    if [ ! -z "${user_id}" ]; then
        sed -i 's/^\(UserId\s\).*$/\1'"${user_id}"'/' "${CONFIG_FILE}"
    fi
    # Request license-key
    echo -n Maxminddb license-key:
    read -rs license_key
    echo
    if [ ! -z "${license_key}" ]; then
        sed -i 's/^\(LicenseKey\s\).*$/\1'"${license_key}"'/' "${CONFIG_FILE}"
    fi
fi
################################### Database ###################################
if [[ (-d "$BASE/druid-base/etc/${CONFIG_NAME}") || (-d "$BASE/saver/etc/${CONFIG_NAME}") ]]; then
    if [ -d "$BASE/druid-base/etc/${CONFIG_NAME}" ]; then
        DRUID_CONFIG_FILE="$BASE/druid-base/etc/${CONFIG_NAME}/common.runtime.properties"
        if [ ! -f "${DRUID_CONFIG_FILE}" ]
        then
            cp "${DRUID_CONFIG_FILE}.dist" "${DRUID_CONFIG_FILE}"
        fi
    fi
    if [ -d "$BASE/saver/etc/${CONFIG_NAME}" ]; then
        SAVER_CONFIG_FILE="$BASE/saver/etc/${CONFIG_NAME}/config.ini"
        if [ ! -f "${SAVER_CONFIG_FILE}" ]
        then
            cp "${SAVER_CONFIG_FILE}.dist" "${SAVER_CONFIG_FILE}"
        fi
    fi
    # Request druid password
    echo -n Database druid password:
    read -rs druid_password
    echo
    if [ ! -z "${druid_password}" ]; then
        if [ ! -z "${DRUID_CONFIG_FILE}" ]; then
            sed -i 's/^\(druid\.metadata\.storage\.connector\.password=\).*$/\1'"${druid_password}"'/' "${DRUID_CONFIG_FILE}"
        fi
        if [ ! -z "${SAVER_CONFIG_FILE}" ]; then
            sed -i 's/^\(password\s*=\s*\).*$/\1'"${druid_password}"'/' "${SAVER_CONFIG_FILE}"
        fi
    fi
fi
################################## S3 storage ##################################
if [[ (-d "$BASE/druid-base/etc/${CONFIG_NAME}") || (-d "$BASE/saver/etc/${CONFIG_NAME}") || (-d "$BASE/filter/etc/${CONFIG_NAME}") ]]; then
    if [ -d "$BASE/druid-base/etc/${CONFIG_NAME}" ]; then
        DRUID_CONFIG_FILE="$BASE/druid-base/etc/${CONFIG_NAME}/common.runtime.properties"
        if [ ! -f "${DRUID_CONFIG_FILE}" ]
        then
            cp "${DRUID_CONFIG_FILE}.dist" "${DRUID_CONFIG_FILE}"
        fi
    fi
    if [ -d "$BASE/saver/etc/${CONFIG_NAME}" ]; then
        SAVER_CONFIG_FILE="$BASE/saver/etc/${CONFIG_NAME}/s3cfg/.s3cfg"
        if [ ! -f "${SAVER_CONFIG_FILE}" ]
        then
            cp "${SAVER_CONFIG_FILE}.dist" "${SAVER_CONFIG_FILE}"
        fi
    fi
    if [ -d "$BASE/filter/etc/${CONFIG_NAME}" ]; then
        FILTER_CONFIG_FILE="$BASE/filter/etc/${CONFIG_NAME}/.s3cfg"
        if [ ! -f "${FILTER_CONFIG_FILE}" ]
        then
            cp "${FILTER_CONFIG_FILE}.dist" "${FILTER_CONFIG_FILE}"
        fi
    fi
    # Request license-key
    echo -n S3 storage access key:
    read -rs access_key
    echo
    if [ ! -z "${access_key}" ]; then
        if [ ! -z "${DRUID_CONFIG_FILE}" ]; then
            sed -i 's/^\(druid\.s3\.accessKey=\).*$/\1'"${access_key}"'/' "${DRUID_CONFIG_FILE}"
        fi
        if [ ! -z "${SAVER_CONFIG_FILE}" ]; then
            sed -i 's/^\(access_key\s*=\s*\).*$/\1'"${access_key}"'/' "${SAVER_CONFIG_FILE}"
        fi
        if [ ! -z "${FILTER_CONFIG_FILE}" ]; then
            sed -i 's/^\(access_key\s*=\s*\).*$/\1'"${access_key}"'/' "${FILTER_CONFIG_FILE}"
        fi
    fi
    # Request secret-key
    echo -n S3 storage secret key:
    read -rs secret_key
    echo
    if [ ! -z "${secret_key}" ]; then
        if [ ! -z "${DRUID_CONFIG_FILE}" ]; then
            sed -i 's#^\(druid\.s3\.secretKey=\).*$#\1'"${secret_key}"'#' "${DRUID_CONFIG_FILE}"
        fi
        if [ ! -z "${SAVER_CONFIG_FILE}" ]; then
            sed -i 's#^\(secret_key\s*=\s*\).*$#\1'"${secret_key}"'#' "${SAVER_CONFIG_FILE}"
        fi
        if [ ! -z "${FILTER_CONFIG_FILE}" ]; then
            sed -i 's#^\(secret_key\s*=\s*\).*$#\1'"${secret_key}"'#' "${FILTER_CONFIG_FILE}"
        fi
    fi
fi
##################################### Udger ####################################
if [ -d "$BASE/filter/etc/${CONFIG_NAME}" ]; then
    CONFIG_FILE="$BASE/filter/etc/${CONFIG_NAME}/udger.conf"
    if [ ! -f "${CONFIG_FILE}" ]
    then
        cp "${CONFIG_FILE}.dist" "${CONFIG_FILE}"
    fi
    # Request access-key
    echo -n Udger access key:
    read -rs access_key
    echo
    if [ ! -z "${access_key}" ]; then
        sed -i 's#^\(access_key\s*=\s*\).*$#\1'"${access_key}"'#' "${CONFIG_FILE}"
    fi
fi
