#!/bin/bash -e
BASE=$(dirname "$0")
# shellcheck source=./.env
source "$BASE/.env"
if [ -z "$CONFIG_NAME" ]; then
    echo "Need to set CONFIG_NAME in .env file."
    exit 1
fi
# Copy default configuration
for folder in druid-base druid-broker druid-coordinator druid-historical druid-middleManager druid-overlord druid-realtime monitoring rsyslog kafka zookeeper geoipupdate saver filter pivot; do
    if [ ! -d "$BASE/$folder/etc/$CONFIG_NAME" ]; then
        cp -pr "$BASE/$folder/etc/default" "$BASE/$folder/etc/$CONFIG_NAME"
    fi;
done
