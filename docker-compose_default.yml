version: '3'

services:
  geoipupdate:
    build: ./geoipupdate
    image: ${BASE_NAME}/geoipupdate
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_geoipupdate
    volumes:
      - ${GEOIP_LOG_DIR}:/var/log/geoipupdate
      - ./geoipupdate/etc/${CONFIG_NAME}/crontab:/etc/geoipupdate/crontab:ro
      - ./geoipupdate/bin/entry.sh:/usr/local/bin/entry.sh:ro
      - ./geoipupdate/etc/${CONFIG_NAME}/GeoIP.conf:/usr/local/etc/GeoIP.conf:ro
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP
    restart: unless-stopped

  mysql:
    build: ./mysql
    image: ${BASE_NAME}/mysql
    container_name: ${BASE_NAME}_mysql
    volumes:
      - ${MYSQL_DATA_DIR}:/var/lib/mysql
      - ${MYSQL_LOG_DIR}:/var/log/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=druid
      - MYSQL_DATABASE=druid
      - MYSQL_USER=druid
      - MYSQL_PASSWORD=druid
    restart: unless-stopped

  adminer:
    image: adminer
    container_name: ${BASE_NAME}_adminer
    hostname: adminer
    ports:
      - 8080:8080
    links:
      - mysql
    restart: unless-stopped

  zookeeper-1:
    image: zookeeper
    container_name: ${BASE_NAME}_zookeeper-1
    hostname: zookeeper-1
    ports:
      - 2181:2181
    environment:
      ZOO_MY_ID: 1
      ZOO_4LW_COMMANDS_WHITELIST: "*"
      # ZOO_SERVERS: server.1=0.0.0.0:2888:3888;2181
    restart: unless-stopped

  kafka:
    build: ./kafka
    image: ${BASE_NAME}/kafka
    container_name: ${BASE_NAME}_kafka
    env_file: docker-compose.env
    hostname: kafka-server
    volumes:
      - ${KAFKA_LOG_DIR}:/var/log
      - ./kafka/etc:/etc/kafka:ro
      - ./kafka/bin:/usr/local/bin:ro
      - ${KAFKA_DATA_DIR}:/data
      - ${KAFKA_TMP_DIR}:/tmp
    links:
      - zookeeper-1
    restart: unless-stopped

  druid-broker-1:
    image: logs/druid-base:latest
    container_name: ${BASE_NAME}_druid-broker-1
    env_file: .env
    volumes:
      - ./druid-base/etc/_common:/etc/druid/_common:ro
      - ./druid-broker-1/etc:/etc/druid/broker:ro
      - ./druid-broker-1/bin:/usr/local/bin:ro
      - ${BASE_LOG_DIR}/druid-broker-1:/var/log/druid
    tmpfs:
      - /var/run/druid
      - /etc/hadoop
    links:
      - zookeeper-1
      - mysql
    ports:
      - 8082:8082
    restart: unless-stopped

  druid-coordinator-1:
    image: logs/druid-base:latest
    container_name: ${BASE_NAME}_druid-coordinator-1
    env_file: .env
    volumes:
      - ./druid-base/etc/_common:/etc/druid/_common:ro
      - ./druid-coordinator-1/etc:/etc/druid/coordinator:ro
      - ./druid-coordinator-1/bin:/usr/local/bin:ro
      - ${BASE_LOG_DIR}/druid-coordinator-1:/var/log/druid
    links:
      - zookeeper-1
      - mysql
    tmpfs:
      - /var/run/druid
      - /etc/hadoop
    ports:
      - 8081:8081
    restart: unless-stopped

  druid-historical-1:
    image: logs/druid-base:latest
    container_name: ${BASE_NAME}_druid-historical-1
    env_file: .env
    volumes:
      - ./druid-base/etc/_common:/etc/druid/_common:ro
      - ./druid-historical-1/etc:/etc/druid/historical:ro
      - ./druid-historical-1/bin:/usr/local/bin:ro
      - ${BASE_LOG_DIR}/druid-historical-1:/var/log/druid
    tmpfs:
      - /var/run/druid
      - /etc/hadoop
    links:
      - druid-coordinator-1
      - zookeeper-1
    ports:
      - 8083:8083
    restart: unless-stopped

  druid-router-1:
    image: logs/druid-base:latest
    container_name: ${BASE_NAME}_druid-router-1
    env_file: .env
    volumes:
      - ./druid-base/etc/_common:/etc/druid/_common:ro
      - ./druid-router-1/etc:/etc/druid/router:ro
      - ./druid-router-1/bin:/usr/local/bin:ro
      - ${BASE_LOG_DIR}/druid-router-1:/var/log/druid
    tmpfs:
      - /var/run/druid
      - /etc/hadoop
    links:
      - druid-coordinator-1
      - zookeeper-1
    ports:
      - 8888:8888
    restart: unless-stopped

  druid-middleManager-1:
    image: logs/druid-base:latest
    container_name: ${BASE_NAME}_druid-middleManager-1
    env_file: .env
    volumes:
      - ./druid-base/etc/_common:/etc/druid/_common:ro
      - ./druid-middleManager-1/etc:/etc/druid/middleManager:ro
      - ./druid-middleManager-1/bin:/usr/local/bin:ro
      - ${BASE_LOG_DIR}/druid-router-1:/var/log/druid
    tmpfs:
      - /var/run/druid
      - /etc/hadoop
    links:
      - druid-coordinator-1
      - zookeeper-1
    ports:
      - 8091:8091
    restart: unless-stopped

  rsyslog:
    build: ./rsyslog
    image: ${BASE_NAME}/rsyslog
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_rsyslog
    hostname: ${BASE_NAME}-rsyslog
    volumes:
      - ${RSYSLOG_LOG_DIR}:/var/log
      - ./rsyslog/etc/${CONFIG_NAME}/rsyslog.d:/etc/rsyslog.d:ro
      - ./rsyslog/etc/${CONFIG_NAME}/rsyslog.conf:/etc/rsyslog.conf:ro
      - ${UPDATE_LOG_DIR}:/data/log/update
      - ${SPOOL_LOG_DIR}:/var/spool/rsyslog
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
      - ./rsyslog/bin/entry.sh:/usr/local/bin/entry.sh:ro
    links:
      - zookeeper-1:zookeeper-1-server
      - kafka:kafka-server
    ports:
      - 34510-34540:34510-34540
    restart: unless-stopped