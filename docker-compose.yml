version: '3.8'

x-filter-common: &filter-common
  build:
    context: ./filter
    args:
      - HOST_GID=1000
      - HOST_UID=1000
  image: ${BASE_NAME}/filter
  depends_on:
    is_kafka_available:
      condition: service_healthy
  env_file: docker-compose.env
  environment:
    - FILTER_ID=1
    - TZ=UTC
  networks:
    - logs_analytics_network
  volumes:
    - /data1/status:/var/status
    - ./filter/bin/default:/root
    - /data1/rsyslog-ingest/main:/var/log
    - ./filter/src/kafkalogfilter:/opt/filter
    - ./filter/src/Hexaparser:/opt/filter/Hexaparser
    - ./filter/etc/${CONFIG_NAME}:/etc/filter
    - ./filter/bin:/usr/local/bin
    - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
    - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "2048m"
  deploy:
    resources:
      limits:
        memory: 64G
  extra_hosts:
    - "host.docker.internal:host-gateway"

services:
  is_kafka_available:
    image: busybox
    healthcheck:
      test: ["CMD", "nc", "-z", "************", "9092"]
      interval: 15s
      timeout: 5s
      retries: 100
    command: ["sh", "-c", "sleep infinity"]
    networks:
      - logs_analytics_network

  redis:
    networks:
      - logs_analytics_network
    build:
      context: ./redis
    tty: true

  filter-0:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=0
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-0

  filter-1:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=1
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-1

  filter-2:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=2
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-2

  filter-3:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=3
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-3

  filter-4:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=4
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-4

  filter-5:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=5
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-5

  filter-6:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=6
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-6

  filter-7:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=7
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-7

  filter-8:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=8
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-8

  filter-9:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=9
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-9

  filter-10:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=10
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-10

  filter-11:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=11
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-11

  filter-12:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=12
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-12

  filter-13:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=13
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-13

  filter-14:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=14
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-14

  filter-15:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=15
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-15

  filter-16:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=16
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-16

  filter-17:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=17
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-17

  filter-18:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=18
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-18

  filter-19:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=19
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-19

  filter-20:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=20
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-20

  filter-21:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=21
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-21

  filter-22:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=22
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-22

  filter-23:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=23
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-23

  filter-24:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=24
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-24

  filter-25:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=25
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-25

  filter-26:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=26
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-26

  filter-27:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=27
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-27

  filter-28:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=28
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-28

  filter-29:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=29
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-29

  filter-30:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=30
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-30

  filter-31:
    <<: *filter-common
    environment:
      - FILTER_ID=1
      - SCALE_NB=31
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-31

networks:
  logs_analytics_network:
    driver: bridge