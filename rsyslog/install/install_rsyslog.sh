#! /bin/sh -e
BASE=$(dirname "$0")
cd "$BASE"

# Setup compiler.
#echo "http://dl-cdn.alpinelinux.org/alpine/edge/testing" >> /etc/apk/repositories
#apk add --update --no-cache alpine-sdk autoconf automake libtool libestr libestr-dev libfastjson libfastjson-dev libgcrypt libgcrypt-dev liblogging liblogging-dev flex bison libnet libuuid util-linux-dev zlib zlib-dev librdkafka librdkafka-dev libmaxminddb libmaxminddb-dev gnutls gnutls-dev py-docutils bsd-compat-headers
apt-get install git make autoconf pkg-config automake libestr-dev libtool libfastjson-dev libgcrypt-dev liblogging-stdlog-dev flex bison libnet-dev uuid-dev util-linux zlib1g-dev librdkafka-dev libmaxminddb-dev gnutls-dev python-docutils libssl-dev python3-dev -y

# Install libestr.
#git clone https://github.com/rsyslog/libestr.git libestr
#git -C libestr checkout tags/v0.1.10 -b v0.1.10
#cd libestr
#autoreconf -fvi
#./configure
#make -j 4
#make install
#cd ..
#rm -rf libestr

# Install libfastjson.
#git clone https://github.com/rsyslog/libfastjson.git libfastjson
#git -C libfastjson checkout tags/v0.99.4 -b v0.99.4
#cd libfastjson
#autoreconf -fvi
#./configure
#make -j 4
#make install
#cd ..
#rm -rf libfastjson

# Install liblognorm.
git clone https://github.com/rsyslog/liblognorm.git liblognorm
git -C liblognorm checkout tags/v2.0.2 -b v2.0.2
cd liblognorm
autoreconf -fvi
./configure
make -j 4
make install
cd ..
rm -rf liblognorm

# Install liblogging.
#git clone https://github.com/rsyslog/liblogging.git liblogging
#git -C liblogging checkout tags/v1.0.5 -b v1.0.5
#cd liblogging
#autoreconf -fvi
#./configure
#make -j 4
#make install
#cd ..
#rm -rf liblogging

# Install libmaxminddb.
#git clone --recursive https://github.com/maxmind/libmaxminddb.git libmaxminddb
#git -C libmaxminddb checkout tags/1.2.0 -b 1.2.0
#cd libmaxminddb
#autoreconf -fvi
#./configure
#make -j 4
#make install
#cd ..
#rm -rf libmaxminddb

# Install librdkafka.
#git clone https://github.com/edenhill/librdkafka.git librdkafka
#git -C librdkafka checkout tags/0.8.6 -b 0.8.6
#cd librdkafka
#./configure
#make -j 4
#make install
#cd ..
#rm -rf librdkafka

# Install librelp.
rm -rf /tmp/librelp
git clone https://github.com/rsyslog/librelp.git librelp
git -C librelp checkout tags/v1.2.12 -b v1.2.12
cd librelp
autoreconf -fvi
./configure
make -j 4
make install
cd ..
rm -rf librelp

# Install rsyslog.
rm -rf /tmp/rsyslog
git clone https://github.com/rsyslog/rsyslog.git rsyslog
git -C rsyslog checkout tags/v8.23.0 -b v8.23.0
cp -r rsyslog-custom/contrib/* rsyslog/contrib/
git -C rsyslog apply ../rsyslog-custom/patch.diff
cd rsyslog
autoreconf -fvi
./configure --enable-imptcp --enable-relp --enable-omkafka --enable-mmdblookup --enable-mmnormalize
make -j 4
make install
cd ..
rm -rf rsyslog

# Clean compiler.
#apk del alpine-sdk autoconf automake libtool libestr-dev libfastjson-dev libgcrypt-dev liblogging-dev flex bison libnet util-linux-dev zlib zlib-dev librdkafka-dev libmaxminddb-dev gnutls-dev py-docutils bsd-compat-headers
