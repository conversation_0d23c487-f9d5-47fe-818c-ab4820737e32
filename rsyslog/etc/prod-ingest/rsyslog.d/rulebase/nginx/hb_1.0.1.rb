## Main rule
rule=:%
  log_format_version:char-to:"%"%
  service_id:char-to:"%"%
  rtt:char-to:"%"%
  rttvar:char-to:"%"%
  snd_cwnd:char-to:"%"%
  rcv_space:char-to:"%"%
  time_local:char-to:"%"%
  remote_addr:char-to:"%"%
  remote_port:char-to:"%"%
  rconnection:char-to:"%"%
  connection_requests:char-to:"%"%
  msec:char-to:"%"%
  server_addr:char-to:"%"%
  server_protocol:char-to:"%"%
  pipe:char-to:"%"%
  host:char-to:"%"%
  remote_user:char-to:"%"%
  body_bytes_sent:char-to:"%"%
  bytes_sent:char-to:"%"%
  content_length:char-to:"%"%
  content_type:char-to:"%""%
  nginx_version:char-to:"%"%
  request_completion:char-to:"%"%
  request_length:char-to:"%"%
  request_time:char-to:"%"%
  status:char-to:"%"%
  request_uri:char-to:"%"%
  request_filename:char-to:"%"%
  request_method:char-to:"%"%
  http_cookie:char-to:"%"%
  http_referer:char-to:"%"%
  http_x_playback_session_id:char-to:"%"%
  http_accept_language:char-to:"%"%
  http_range:char-to:"%"%
  http_x_forwarded_for:char-to:"%"%
  http_user_agent:char-to:"%"%
  upstream_addr:char-to:"%"%
  upstream_cache_status:char-to:"%"%
  upstream_response_time:char-to:"%"%
  upstream_response_length:char-to:"%"%
  upstream_status:char-to:"%"%
  secure_link:char-to:"%"%
  secure_link_expires:rest%
