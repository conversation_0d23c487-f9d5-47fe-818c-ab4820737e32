## Main rule
rule=:%
  log_format_version:char-sep:"%"%
  service_id:char-sep:"%"%
  rtt:char-sep:"%"%
  rttvar:char-sep:"%"%
  snd_cwnd:char-sep:"%"%
  rcv_space:char-sep:"%"%
  time_local:char-sep:"%"%
  remote_addr:char-sep:"%"%
  remote_port:char-sep:"%"%
  rconnection:char-sep:"%"%
  connection_requests:char-sep:"%"%
  msec:char-sep:"%"%
  server_addr:char-sep:"%"%
  server_protocol:char-sep:"%"%
  pipe:char-sep:"%"%
  host:char-sep:"%"%
  remote_user:char-sep:"%"%
  body_bytes_sent:char-sep:"%"%
  bytes_sent:char-sep:"%"%
  content_length:char-sep:"%"%
  content_type:char-sep:"%"%
  https:char-sep:"%"%
  nginx_version:char-sep:"%"%
  request_completion:char-sep:"%"%
  request_length:char-sep:"%"%
  request_time:char-sep:"%"%
  status:char-sep:"%"%
  request_uri:char-sep:"%"%
  request_filename:char-sep:"%"%
  request_method:char-sep:"%"%
  http_cookie:char-sep:"%"%
  http_referer:char-sep:"%"%
  http_x_playback_session_id:char-sep:"%"%
  http_accept_language:char-sep:"%"%
  http_range:char-sep:"%"%
  http_x_forwarded_for:char-sep:"%"%
  http_user_agent:char-sep:"%"%
  upstream_addr:char-sep:"%"%
  upstream_cache_status:char-sep:"%"%
  upstream_response_time:char-sep:"%"%
  upstream_response_length:char-sep:"%"%
  upstream_status:char-sep:"%"%
  secure_link:char-sep:"%"%
  secure_link_expires:rest%
