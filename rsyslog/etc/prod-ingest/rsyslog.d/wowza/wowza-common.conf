action(
  type="mmdblookup"
  mmdbfile="/usr/local/share/GeoIP/GeoIP2-City.mmdb"
  alias="!iploc"
  fields=[
    "!city!geoname_id",
    "!continent!geoname_id",
    "!country!geoname_id",
    "!location!latitude",
    "!location!longitude",
    "!location!metro_code",
    "!location!time_zone",
    "!postal!code",
    "!subdivisions[0]!geoname_id"
  ]
  key="$!c_ip"
)
action(
  type="mmdblookup"
  mmdbfile="/usr/local/share/GeoIP/GeoIP2-ISP.mmdb"
  alias="!ipisp"
  fields=[
    "!autonomous_system_number"
  ]
  key="$!c_ip"
)
set $!usr!filter_id = cnum(field($!remote_addr, ".", 4)) % 3;
action(
  broker=["kafka-server:9092"]
  type="omkafka"
;   dynatopic="on"
  topic="all_service_entries_dynatopic"
  template="json_wowza_all_format"
;   partitions.auto="on"
  confParam=[
;     "compression.codec=snappy",
    "socket.timeout.ms=5000",
    "socket.keepalive.enable=true"
  ]
)
