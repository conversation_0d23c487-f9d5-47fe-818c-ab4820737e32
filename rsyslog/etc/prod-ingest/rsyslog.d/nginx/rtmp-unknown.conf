# log every host in its own directory
#$template StorageNginxUnknown,"/var/log/rsyslog/nginx-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
#*.* ?StorageNginxUnknown
*.* :mmnormalize:            # applies normalization to all messages

if (strlen($!remote_addr) != 0) then {
  $includeConfig /etc/rsyslog.d/nginx/rtmp-common.conf
} else {
  $template FailRtmpUnknown,"/var/log/rsyslog/fail/rtmp-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
  *.* ?FailRtmpUnknown
}
