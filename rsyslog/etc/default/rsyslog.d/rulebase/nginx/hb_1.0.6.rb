## Main rule
rule=:%
  log_format_version:char-sep:"%"%
  service_id:char-sep:"%"%
  time_local:char-sep:"%"%
  remote_addr:char-sep:"%"%
  msec:interpret:float:char-sep:"%"%
  server_addr:char-sep:"%"%
  server_protocol:char-sep:"%"%
  host:char-sep:"%"%
  body_bytes_sent:interpret:base10int:char-sep:"%"%
  bytes_sent:interpret:base10int:char-sep:"%"%
  https:char-sep:"%"%
  request_completion:char-sep:"%"%
  request_length:interpret:base10int:char-sep:"%"%
  request_time:interpret:float:char-sep:"%"%
  status:interpret:base10int:char-sep:"%"%
  request_uri:char-sep:"%"%
  request_filename:char-sep:"%"%
  request_method:char-sep:"%"%
  http_cookie:char-sep:"%"%
  http_referer:char-sep:"%"%
  http_x_playback_session_id:char-sep:"%"%
  http_accept_language:char-sep:"%"%
  http_range:char-sep:"%"%
  http_x_forwarded_for:char-sep:"%"%
  http_via:char-sep:"%"%
  http_user_agent:char-sep:"%"%
  upstream_addr:char-sep:"%"%
  upstream_cache_status:char-sep:"%"%
  upstream_response_time:char-sep:"%"%
  upstream_response_length:char-sep:"%"%
  upstream_status:char-sep:"%"%
  secure_link:char-sep:"%"%
  secure_link_expires:rest%
