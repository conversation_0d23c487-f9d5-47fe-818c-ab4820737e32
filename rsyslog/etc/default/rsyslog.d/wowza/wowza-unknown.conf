# log every host in its own directory
#$template StorageWowzaUnknown,"/var/log/rsyslog/wowza-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
#*.* ?StorageWowzaUnknown
*.* :mmnormalize:            # applies normalization to all messages

if (strlen($!c_ip) != 0) then {
  $includeConfig /etc/rsyslog.d/wowza/wowza-common.conf
} else {
  $template FailWowzaUnknown,"/var/log/rsyslog/fail/wowza-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
  *.* ?FailWowzaUnknown
}

