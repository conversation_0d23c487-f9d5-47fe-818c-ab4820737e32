### Global configuration
$AllowedSender TCP, 195.178.112.0/23
$AllowedSender TCP, 62.210.86.143
$AllowedSender TCP, 62.210.140.147
$AllowedSender TCP, 158.69.119.69
$AllowedSender TCP, 62.210.189.63
$AllowedSender TCP, 62.210.143.111
$AllowedSender TCP, 94.23.206.155
$AllowedSender TCP, 149.202.203.124
$AllowedSender TCP, 127.0.0.1
### Module imptcp
module(load="imptcp" threads="4")
    input(type="imptcp" port="34510")
input(type="imptcp" port="34511")
input(type="imptcp" port="34512")
input(type="imptcp" port="34513")
input(type="imptcp" port="34514")
input(type="imptcp" port="34515")
input(type="imptcp" port="34520")
input(type="imptcp" Port="34530")
### Module mmdblookup
module(load="mmdblookup")
### Module omkafka
module(load="omkafka")
### Module mmnormalize
module(load="mmnormalize")
$mmnormalizeUseRawMSG on

main_queue(
  queue.type="fixedArray"
  queue.workerthreads="4"       # threads to work on the queue
  queue.dequeueBatchSize="4096" # max number of messages to process at once
  queue.size="250000"           # max queue size
)

template(name="test_json" type="list"){
  property(name="jsonmesg")
  constant(value="\n")
}

template(name="json_nginx_format" type="list" option.json="on"){
  # Syslog data
  constant(value="{\"fromhost\":\"")
  property(name="hostname")
  constant(value="\",\"fromhost_ip\":\"")
  property(name="fromhost-ip")
  constant(value="\",\"syslog_tag\":\"")
  property(name="syslogtag")

  # Remote data
  constant(value="\",\"service_id\":\"")
  property(name="$!service_id")
  constant(value="\",\"time_local\":\"")
  property(name="$!time_local")
  constant(value="\",\"remote_addr\":\"")
  property(name="$!remote_addr")
  constant(value="\",\"msec\":\"")
  property(name="$!msec")
  constant(value="\",\"server_addr\":\"")
  property(name="$!server_addr")
  constant(value="\",\"server_protocol\":\"")
  property(name="$!server_protocol")
  constant(value="\",\"host\":\"")
  property(name="$!host")
  constant(value="\",\"body_bytes_sent\":\"")
  property(name="$!body_bytes_sent")
  constant(value="\",\"bytes_sent\":\"")
  property(name="$!bytes_sent")
  constant(value="\",\"https\":\"")
  property(name="$!https")
  constant(value="\",\"request_completion\":\"")
  property(name="$!request_completion")
  constant(value="\",\"request_length\":\"")
  property(name="$!request_length")
  constant(value="\",\"request_time\":\"")
  property(name="$!request_time")
  constant(value="\",\"status\":\"")
  property(name="$!status")
  constant(value="\",\"request_uri\":\"")
  property(name="$!request_uri")
  constant(value="\",\"request_filename\":\"")
  property(name="$!request_filename")
  constant(value="\",\"request_method\":\"")
  property(name="$!request_method")
  constant(value="\",\"http_referer\":\"")
  property(name="$!http_referer")
  constant(value="\",\"http_x_playback_session_id\":\"")
  property(name="$!http_x_playback_session_id")
  constant(value="\",\"http_accept_language\":\"")
  property(name="$!http_accept_language")
  constant(value="\",\"http_x_forwarded_for\":\"")
  property(name="$!http_x_forwarded_for")
  constant(value="\",\"http_via\":\"")
  property(name="$!http_via")
  constant(value="\",\"http_user_agent\":\"")
  property(name="$!http_user_agent")
  constant(value="\",\"upstream_addr\":\"")
  property(name="$!upstream_addr")
  constant(value="\",\"upstream_cache_status\":\"")
  property(name="$!upstream_cache_status")
  constant(value="\",\"upstream_response_time\":\"")
  property(name="$!upstream_response_time")
  constant(value="\",\"upstream_response_length\":\"")
  property(name="$!upstream_response_length")
  constant(value="\",\"upstream_status\":\"")
  property(name="$!upstream_status")
  constant(value="\",\"secure_link\":\"")
  property(name="$!secure_link")
  constant(value="\",\"secure_link_expires\":\"")
  property(name="$!secure_link_expires")

  # GeoIp data
  constant(value="\",\"city_geoname_id\":\"")
  property(name="$!iploc!city!geoname_id")
  constant(value="\",\"continent_geoname_id\":\"")
  property(name="$!iploc!continent!geoname_id")
  constant(value="\",\"country_geoname_id\":\"")
  property(name="$!iploc!country!geoname_id")
  constant(value="\",\"latitude\":\"")
  property(name="$!iploc!location!latitude")
  constant(value="\",\"longitude\":\"")
  property(name="$!iploc!location!longitude")
  constant(value="\",\"metro_code\":\"")
  property(name="$!iploc!location!metro_code")
  constant(value="\",\"time_zone\":\"")
  property(name="$!iploc!location!time_zone")
  constant(value="\",\"postal_code\":\"")
  property(name="$!iploc!postal!code")
  constant(value="\",\"subdiv_1_geoname_id\":\"")
  property(name="$!iploc!subdivisions!0!geoname_id")
  constant(value="\",\"isp_asn\":\"")
  property(name="$!ipisp!autonomous_system_number")
  constant(value="\"}\n")
}

template(name="json_wowza_all_format" type="list" option.json="on"){
  # Syslog data
  constant(value="{\"fromhost\":\"")
  property(name="hostname")
  constant(value="\",\"fromhost_ip\":\"")
  property(name="fromhost-ip")
  constant(value="\",\"syslog_tag\":\"")
  property(name="syslogtag")

  # Remote data
  constant(value="\",\"date\":\"")
  property(name="$!date")
  constant(value="\",\"time\":\"")
  property(name="$!time")
  constant(value="\",\"tz\":\"")
  property(name="$!tz")
  constant(value="\",\"x_event\":\"")
  property(name="$!x_event")
  constant(value="\",\"x_category\":\"")
  property(name="$!x_category")
  constant(value="\",\"x_severity\":\"")
  property(name="$!x_severity")
  constant(value="\",\"x_status\":\"")
  property(name="$!x_status")
  constant(value="\",\"x_ctx\":\"")
  property(name="$!x_ctx")
  constant(value="\",\"x_comment\":\"")
  property(name="$!x_comment")
  constant(value="\",\"x_vhost\":\"")
  property(name="$!x_vhost")
  constant(value="\",\"x_app\":\"")
  property(name="$!x_app")
  constant(value="\",\"x_appinst\":\"")
  property(name="$!x_appinst")
  constant(value="\",\"x_duration\":\"")
  property(name="$!x_duration")
  constant(value="\",\"s_ip\":\"")
  property(name="$!s_ip")
  constant(value="\",\"s_port\":\"")
  property(name="$!s_port")
  constant(value="\",\"s_uri\":\"")
  property(name="$!s_uri")
  constant(value="\",\"c_ip\":\"")
  property(name="$!c_ip")
  constant(value="\",\"c_proto\":\"")
  property(name="$!c_proto")
  constant(value="\",\"c_referrer\":\"")
  property(name="$!c_referrer")
  constant(value="\",\"c_user_agent\":\"")
  property(name="$!c_user_agent")
  constant(value="\",\"c_client_id\":\"")
  property(name="$!c_client_id")
  constant(value="\",\"cs_bytes\":\"")
  property(name="$!cs_bytes")
  constant(value="\",\"sc_bytes\":\"")
  property(name="$!sc_bytes")
  constant(value="\",\"x_stream_id\":\"")
  property(name="$!x_stream_id")
  constant(value="\",\"x_spos\":\"")
  property(name="$!x_spos")
  constant(value="\",\"cs_stream_bytes\":\"")
  property(name="$!cs_stream_bytes")
  constant(value="\",\"sc_stream_bytes\":\"")
  property(name="$!sc_stream_bytes")
  constant(value="\",\"x_sname\":\"")
  property(name="$!x_sname")
  constant(value="\",\"x_sname_query\":\"")
  property(name="$!x_sname_query")
  constant(value="\",\"x_file_name\":\"")
  property(name="$!x_file_name")
  constant(value="\",\"x_file_ext\":\"")
  property(name="$!x_file_ext")
  constant(value="\",\"x_file_size\":\"")
  property(name="$!x_file_size")
  constant(value="\",\"x_file_length\":\"")
  property(name="$!x_file_length")
  constant(value="\",\"x_suri\":\"")
  property(name="$!x_suri")
  constant(value="\",\"x_suri_stem\":\"")
  property(name="$!x_suri_stem")
  constant(value="\",\"x_suri_query\":\"")
  property(name="$!x_suri_query")
  constant(value="\",\"cs_uri_stem\":\"")
  property(name="$!cs_uri_stem")
  constant(value="\",\"cs_uri_query\":\"")
  property(name="$!cs_uri_query")

  # GeoIp data
  constant(value="\",\"city_geoname_id\":\"")
  property(name="$!iploc!city!geoname_id")
  constant(value="\",\"continent_geoname_id\":\"")
  property(name="$!iploc!continent!geoname_id")
  constant(value="\",\"country_geoname_id\":\"")
  property(name="$!iploc!country!geoname_id")
  constant(value="\",\"latitude\":\"")
  property(name="$!iploc!location!latitude")
  constant(value="\",\"longitude\":\"")
  property(name="$!iploc!location!longitude")
  constant(value="\",\"metro_code\":\"")
  property(name="$!iploc!location!metro_code")
  constant(value="\",\"time_zone\":\"")
  property(name="$!iploc!location!time_zone")
  constant(value="\",\"postal_code\":\"")
  property(name="$!iploc!postal!code")
  constant(value="\",\"subdiv_1_geoname_id\":\"")
  property(name="$!iploc!subdivisions!0!geoname_id")
  constant(value="\",\"isp_asn\":\"")
  property(name="$!ipisp!autonomous_system_number")
  constant(value="\"}\n")
}

template(name="json_rtmp_all_format" type="list" option.json="on"){
  # Syslog data
  constant(value="{\"fromhost\":\"")
  property(name="hostname")
  constant(value="\",\"fromhost_ip\":\"")
  property(name="fromhost-ip")
  constant(value="\",\"syslog_tag\":\"")
  property(name="syslogtag")

  # Remote data
  constant(value="\",\"service_id\":\"")
  property(name="$!service_id")
  constant(value="\",\"time_local\":\"")
  property(name="$!time_local")
  constant(value="\",\"msec\":\"")
  property(name="$!msec")
  constant(value="\",\"connection\":\"")
  property(name="$!connection")
  constant(value="\",\"remote_addr\":\"")
  property(name="$!remote_addr")
  constant(value="\",\"app\":\"")
  property(name="$!app")
  constant(value="\",\"name\":\"")
  property(name="$!name")
  constant(value="\",\"args\":\"")
  property(name="$!args")
  constant(value="\",\"flashver\":\"")
  property(name="$!flashver")
  constant(value="\",\"swfurl\":\"")
  property(name="$!swfurl")
  constant(value="\",\"tcurl\":\"")
  property(name="$!tcurl")
  constant(value="\",\"pageurl\":\"")
  property(name="$!pageurl")
  constant(value="\",\"command\":\"")
  property(name="$!command")
  constant(value="\",\"bytes_sent\":\"")
  property(name="$!bytes_sent")
  constant(value="\",\"bytes_received\":\"")
  property(name="$!bytes_received")
  constant(value="\",\"session_time\":\"")
  property(name="$!session_time")

  # GeoIp data
  constant(value="\",\"city_geoname_id\":\"")
  property(name="$!iploc!city!geoname_id")
  constant(value="\",\"continent_geoname_id\":\"")
  property(name="$!iploc!continent!geoname_id")
  constant(value="\",\"country_geoname_id\":\"")
  property(name="$!iploc!country!geoname_id")
  constant(value="\",\"latitude\":\"")
  property(name="$!iploc!location!latitude")
  constant(value="\",\"longitude\":\"")
  property(name="$!iploc!location!longitude")
  constant(value="\",\"metro_code\":\"")
  property(name="$!iploc!location!metro_code")
  constant(value="\",\"time_zone\":\"")
  property(name="$!iploc!location!time_zone")
  constant(value="\",\"postal_code\":\"")
  property(name="$!iploc!postal!code")
  constant(value="\",\"subdiv_1_geoname_id\":\"")
  property(name="$!iploc!subdivisions!0!geoname_id")
  constant(value="\",\"isp_asn\":\"")
  property(name="$!ipisp!autonomous_system_number")
  constant(value="\"}\n")
}

template(
  name="syslog_hourly_update_dynafile"
  type="string"
  string="/data/log/update/%$YEAR%/%$MONTH%/%$DAY%/%$HOUR%/%$MYHOSTNAME%/%HOSTNAME%#%FROMHOST%#%FROMHOST-IP%#%SYSLOGTAG%.log"
)

template(
  name="syslog_hourly_update_template"
  type="string"
  string="%MSG%\n"
)

template(
  name="all_service_entries_dynatopic"
  type="string"
  string="all-service-entries-%$!usr!filter_id%"
)

#nginx pas anthony
#suppression des espaces devant hb
if ($syslogtag == 'nginx') then {
  if ($msg startswith 'hb_1.0.5"') then {
    $mmnormalizeRuleBase /etc/rsyslog.d/rulebase/nginx/hb_1.0.5.rb
    $includeConfig /etc/rsyslog.d/nginx/nginx-unknown.conf
  } else if ($msg startswith 'hb_2.0.0"') then {
    $mmnormalizeRuleBase /etc/rsyslog.d/rulebase/nginx/hb_2.0.0.rb
    $includeConfig /etc/rsyslog.d/nginx/rtmp-unknown.conf
  } else if ($msg startswith 'hb_1.0.6"') then {
    $mmnormalizeRuleBase /etc/rsyslog.d/rulebase/nginx/hb_1.0.6.rb
    $includeConfig /etc/rsyslog.d/nginx/nginx-unknown.conf
  } else if ($msg startswith 'hb_1.0.4"') then {
    $mmnormalizeRuleBase /etc/rsyslog.d/rulebase/nginx/hb_1.0.4.rb
    $includeConfig /etc/rsyslog.d/nginx/nginx-unknown.conf
  } else {
    action(
      type="omfile"
      dirCreateMode="0700"
      FileCreateMode="0644"
      File="/var/log/rsyslog/orphan_version_messages"
    )
  }
} else if ($syslogtag == 'wowza') then {
  $mmnormalizeRuleBase /etc/rsyslog.d/rulebase/wowza/wowza-escape-log.rb
  $includeConfig /etc/rsyslog.d/wowza/wowza-unknown.conf
} else {
  action(
    type="omfile"
    dirCreateMode="0700"
    FileCreateMode="0644"
    File="/var/log/rsyslog/orphan_messages"
  )
}

#action(
#    type="omfile"
#    template="test_json"
#    dirCreateMode="0700"
#   FileCreateMode="0644"
#    File="/var/log/rsyslog/test42"
#)

action(
  type="omfile"
  action.resumeRetryCount="-1"
  template="syslog_hourly_update_template"
  dynaFile="syslog_hourly_update_dynafile"
  dynaFileCacheSize="50"
  dirCreateMode="0700"
  fileCreateMode="0600"
  flushInterval="2"
  ioBufferSize="128k"
  flushOnTXEnd="off"
  asyncWriting="on"
  queue.type="fixedArray"
  queue.size="250000"           # max queue size
  queue.timeoutenqueue="0"      # 0 means discard immediate
  queue.dequeueBatchSize="4096" # max number of messages to process at once
  queue.workerthreads="4"       # threads to work on the queue
  queue.workerThreadMinimumMessages="25000"
  queue.saveOnShutdown="on"
  queue.filename="log_save"
  queue.maxdiskspace="1000000000"
)
stop
