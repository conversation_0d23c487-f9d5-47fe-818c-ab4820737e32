# log every host in its own directory
#$template StorageNginxUnknown,"/var/log/rsyslog/nginx-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
#*.* ?StorageNginxUnknown
*.* :mmnormalize:            # applies normalization to all messages

if (strlen($!remote_addr) != 0) then {
  $includeConfig /etc/rsyslog.d/nginx/nginx-common.conf
} else {
  $template FailNginxUnknown,"/var/log/rsyslog/fail/nginx-unknown/%HOSTNAME%/%$YEAR%/%$MONTH%/%$DAY%/syslog.log"
  *.* ?FailNginxUnknown
}
