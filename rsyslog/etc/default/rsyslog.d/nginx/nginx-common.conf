action(
  type="mmdblookup"
  mmdbfile="/usr/local/share/GeoIP/GeoIP2-City.mmdb"
  alias="!iploc"
  fields=[
    "!city!geoname_id",
    "!continent!geoname_id",
    "!country!geoname_id",
    "!location!latitude",
    "!location!longitude",
    "!location!metro_code",
    "!location!time_zone",
    "!postal!code",
    "!subdivisions[0]!geoname_id"
  ]
  key="$!remote_addr"
)
action(
  type="mmdblookup"
  mmdbfile="/usr/local/share/GeoIP/GeoIP2-ISP.mmdb"
  alias="!ipisp"
  fields=[
    "!autonomous_system_number"
  ]
  key="$!remote_addr"
)

#TESTTESTTESTTESTTESTTESTTESTTESTTEST

action(
  type="omfile"
  template="json_nginx_format"
  dirCreateMode="0700"
  FileCreateMode="0644"
  File="/var/log/rsyslog/logsResults"
)

#TESTTESTTESTTESTTESTTESTTESTTESTTEST


set $!usr!filter_id = cnum(field($!remote_addr, ".", 4)) % 3;
action(
#    broker=["kafka-server:9092"]
  broker=["localhost:9092"]
  type="omkafka"
   #dynatopic="on"
  topic="all-service-entries"
  template="json_nginx_format"
#   partitions.auto="on"
  confParam=[
    "socket.timeout.ms=5000",
    "socket.keepalive.enable=true"
  ]
)
