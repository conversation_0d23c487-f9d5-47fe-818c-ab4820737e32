FROM ubuntu:18.04

MAINTAINER <PERSON><PERSON><PERSON> <<EMAIL>>

# Copy install scripts.
COPY install /tmp

# Copy rsyslog-custom sources.
COPY src /tmp

RUN apt-get update && \
    apt-get upgrade -y && \
   /tmp/install_rsyslog.sh && \
   rm -rf /var/cache/apk/* /tmp/*

RUN apt-get install -y rsyslog librelp0 rsyslog-relp rsyslog-kafka

EXPOSE 34510-34540

VOLUME /var/log \
       /etc/rsyslog.d \
#       /etc/rsyslog.conf \
       /data/log/update \
       /var/spool/rsyslog \
       /usr/local/share/GeoIP \
       /usr/local/bin/entry.sh

CMD ["/usr/local/bin/entry.sh"]
