{"type": "index", "spec": {"dataSchema": {"dataSource": "all-filtered-wowza-log-entries", "parser": {"type": "string", "parseSpec": {"format": "json", "timestampSpec": {"column": "timestamp_ms", "format": "millis"}, "dimensionsSpec": {"dimensions": ["service_id", "playlist_id", "fromhost", "fromhost_ip", "x_event", "x_category", "x_severity", "x_status", "x_ctx", "x_comment", "x_vhost", "x_app", "x_appinst", "s_ip", "s_port", "s_uri", "c_ip", "c_proto", "c_referrer", "c_user_agent", "c_client_id", "x_stream_id", "x_spos", "x_sname", "x_sname_query", "x_file_name", "x_file_ext", "x_file_size", "x_file_length", "x_suri", "x_suri_stem", "x_suri_query", "cs_uri_stem", "cs_uri_query", "language", "quality", "media_id", "isp_asn", "os_name", "ua_name", "device_type", "browser_type"], "dimensionExclusions": [], "spatialDimensions": []}}}, "metricsSpec": [{"type": "count", "name": "count"}, {"type": "longSum", "name": "cs_bytes", "fieldName": "cs_bytes"}, {"type": "longSum", "name": "sc_bytes", "fieldName": "sc_bytes"}, {"type": "longSum", "name": "cs_stream_bytes", "fieldName": "cs_stream_bytes"}, {"type": "longSum", "name": "sc_stream_bytes", "fieldName": "sc_stream_bytes"}, {"type": "doubleSum", "name": "x_duration", "fieldName": "x_duration"}, {"type": "hyperUnique", "name": "unique_media_id", "fieldName": "media_id"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "MINUTE", "intervals": ["<<START_INTERVAL_DATE>>/<<END_INTERVAL_DATE>>"]}}, "ioConfig": {"type": "index", "firehose": {"type": "local", "baseDir": "<<LOCAL_BASE_DIR>>", "filter": "all-filtered-wowza-log-entries.log.*"}}, "tuningConfig": {"type": "index"}}}