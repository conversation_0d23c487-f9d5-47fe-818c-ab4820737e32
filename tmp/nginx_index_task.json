{"type": "index", "spec": {"dataSchema": {"dataSource": "all-filtered-nginx-log-entries", "parser": {"type": "string", "parseSpec": {"format": "json", "timestampSpec": {"column": "timestamp_ms", "format": "millis"}, "dimensionsSpec": {"dimensions": ["service_id", "playlist_id", "fromhost", "fromhost_ip", "stream_time_ms", "stream_index", "server_addr", "server_protocol", "host", "status", "request_uri", "request_filename", "request_method", "https", "request_completion", "upstream_addr", "upstream_cache_status", "upstream_status", "secure_link", "extension", "language", "quality", "media_id", "isp_asn", "os_name", "os_family", "ua_name", "ua_family", "device_type", "browser_type", "remote_addr", "http_referer", "http_x_playback_session_id", "http_accept_language", "http_user_agent", "http_x_forwarded_for", "http_via", "secure_link_expires"], "dimensionExclusions": [], "spatialDimensions": []}}}, "metricsSpec": [{"type": "count", "name": "count"}, {"type": "longSum", "name": "body_bytes_sent", "fieldName": "body_bytes_sent"}, {"type": "longSum", "name": "bytes_sent", "fieldName": "bytes_sent"}, {"type": "longSum", "name": "request_length", "fieldName": "request_length"}, {"type": "doubleSum", "name": "request_time", "fieldName": "request_time"}, {"type": "doubleSum", "name": "upstream_response_time", "fieldName": "upstream_response_time"}, {"type": "longSum", "name": "upstream_response_length", "fieldName": "upstream_response_length"}, {"type": "doubleSum", "name": "duration_s", "fieldName": "duration_s"}, {"type": "hyperUnique", "name": "unique_media_id", "fieldName": "media_id"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "MINUTE", "intervals": ["<<START_INTERVAL_DATE>>/<<END_INTERVAL_DATE>>"]}}, "ioConfig": {"type": "index", "firehose": {"type": "local", "baseDir": "<<LOCAL_BASE_DIR>>", "filter": "all-filtered-nginx-log-entries.log.*"}}, "tuningConfig": {"type": "index"}}}