{"type": "index", "spec": {"dataSchema": {"dataSource": "all-filtered-rtmp-log-entries", "parser": {"type": "string", "parseSpec": {"format": "json", "timestampSpec": {"column": "timestamp_ms", "format": "millis"}, "dimensionsSpec": {"dimensions": ["service_id", "playlist_id", "fromhost", "fromhost_ip", "connection", "remote_addr", "app", "name", "args", "flash_version", "swf_url", "tc_url", "page_url", "command"], "dimensionExclusions": [], "spatialDimensions": []}}}, "metricsSpec": [{"type": "count", "name": "count"}, {"type": "longSum", "name": "bytes_received", "fieldName": "bytes_received"}, {"type": "longSum", "name": "bytes_sent", "fieldName": "bytes_sent"}, {"type": "longSum", "name": "session_time", "fieldName": "session_time"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "MINUTE", "intervals": ["<<START_INTERVAL_DATE>>/<<END_INTERVAL_DATE>>"]}}, "ioConfig": {"type": "index", "firehose": {"type": "local", "baseDir": "<<LOCAL_BASE_DIR>>", "filter": "all-filtered-rtmp-log-entries.log.*"}}, "tuningConfig": {"type": "index"}}}