version: '3'

services:
  is_kafka_available:
    image: busybox
    healthcheck:
      test: ["CMD", "nc", "-z", "kafkahost", "9092"]
      interval: 15s
      timeout: 5s
      retries: 100
    command: ["sh", "-c", "sleep infinity"]  # Keeps the container running
    networks:
      - logs_analytics_network

  redis:
    networks:
      - logs_analytics_network
    build:
      context: ./redis
    tty: true

  filter-0:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=0
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-0
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-1:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=1
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-1
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-2:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=2
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-2
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-3:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=3
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-3
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-4:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=4
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-4
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-5:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=5
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-5
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-6:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=6
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-6
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-7:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=7
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-7
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-8:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=8
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-8
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "2048m"
    deploy:
      resources:
        limits:
          memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"

  filter-9:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=9
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-9
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-10:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=10
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-10
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-11:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=11
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-11
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-12:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=12
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-12
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-13:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=13
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-13
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-14:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=14
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-14
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


  filter-15:
    build:
      context: ./filter
      args:
        - HOST_GID=1007
        - HOST_UID=1007
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=15
      - TZ=UTC
    container_name: ${BASE_NAME}_filter-15
    networks:
      - logs_analytics_network


    volumes:
      - /data1/status:/var/status
      - ./filter/bin/default:/root
      - /data1/rsyslog-ingest/main:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
    logging:
        driver: "json-file"
        options:
          max-size: "2048m"
    deploy:
        resources:
          limits:
            memory: 64G
    extra_hosts:
      - "host.docker.internal:host-gateway"


networks:
  logs_analytics_network:
    driver: bridge
