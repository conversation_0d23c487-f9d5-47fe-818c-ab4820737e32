#!/bin/bash

NUM_CONTAINERS=32
TAIL_LINES=40
VIEW_TIME=10  # seconds per container
WAIT_BETWEEN=5  # seconds to wait between containers
CYCLES=1  # number of complete cycles through all containers (0 for infinite)

cycle_count=0

while [ $CYCLES -eq 0 ] || [ $cycle_count -lt $CYCLES ]; do
  echo "===== Starting cycle $((cycle_count + 1)) ====="
  
  for i in $(seq 0 $((NUM_CONTAINERS - 1))); do
    CONTAINER="logs_filter-$i"
    echo
    echo "===== Viewing $CONTAINER for $VIEW_TIME seconds ====="
    
    # Check if container exists
    if docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER}$"; then
      # First show the last N lines
      docker logs --tail $TAIL_LINES $CONTAINER
    else
      echo "Container $CONTAINER not found"
    fi
    
    # Wait between containers (except after the last one)
    if [ $i -lt $((NUM_CONTAINERS - 1)) ]; then
      sleep $WAIT_BETWEEN
    fi
  done
  
  cycle_count=$((cycle_count + 1))
  
  # If doing multiple cycles, wait before starting the next cycle
  if [ $CYCLES -eq 0 ] || [ $cycle_count -lt $CYCLES ]; then
    echo
    echo "===== Cycle $cycle_count complete. Waiting before next cycle... ====="
    sleep $WAIT_BETWEEN
  fi
done

echo "===== All cycles complete ====="
