GeoIP.conf file - used by geoipupdate program
# to update databases from http://www.maxmind.com
# UserId, LicenseKey, <NAME_EMAIL> account
UserId <user-id>
LicenseKey <license-key>
ProductIds GeoIP2-City GeoIP2-ISP

# The remaining settings are OPTIONAL.

# The directory to store the database files. Defaults to /usr/local/share/GeoIP
# DatabaseDirectory /usr/local/share/GeoIP

# The server to use. Defaults to "updates.maxmind.com".
# Host updates.maxmind.com

# The desired protocol either "https" (default) or "http".
# Protocol https

# The proxy host name or IP address. You may optionally specify a
# port number, e.g., 127.0.0.1:8888. If no port number is specified, 1080
# will be used.
# Proxy 127.0.0.1:8888

# The user name and password to use with your proxy server.
# ProxyUserPassword username:password

# Whether to skip host name verification on HTTPS connections.
# Defaults to "0".
# SkipHostnameVerification 0

# Whether to skip peer verification on HTTPS connections.
# Defaults to "0".
# SkipPeerVerification 0