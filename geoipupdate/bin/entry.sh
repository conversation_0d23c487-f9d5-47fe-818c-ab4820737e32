#!/bin/sh -e
#sleep 3000
export PATH=$PATH:/root/go/bin/

getent group geoipupdate &>/dev/null || ( \
    addgroup geoipupdate -g "$HOST_GID" && \
    adduser -G geoipupdate -s /bin/sh -D geoipupdate -u "$HOST_UID"
)

chown geoipupdate:geoipupdate /var/log/geoipupdate /usr/local/share/GeoIP
chmod 701 root/
chmod 777 /usr/local/share/GeoIP/.geoipupdate.lock
chmod 777 /usr/local/share/GeoIP
chmod 777 /usr/local/share
chmod 777 /usr/local
chmod 777 /usr

su -m geoipupdate -c geoipupdate

chmod u+s /bin/busybox


crontab -u geoipupdate /etc/geoipupdate/crontab
exec crond -f -l 7 -L /dev/stdout
