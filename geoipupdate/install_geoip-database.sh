#!/bin/bash -e
display_usage(){
    echo -e "Usage:$0 license-key"
}
if [ $# -le 0 ]
then
    display_usage
    exit 1
fi
BASE=$(dirname "$0")
SRC_DIR=$(cd "$BASE"; pwd)/geoipdownload
mkdir -p "$SRC_DIR"
rm -rf "/tmp/geoipdownload"
mkdir "/tmp/geoipdownload"
curl "https://download.maxmind.com/app/geoip_download?edition_id=GeoIP2-ISP&date=20140819&suffix=tar.gz&license_key=$1" -o "/tmp/geoipdownload/GeoIP2-ISP_20140819.tar.gz"
tar zxf "/tmp/geoipdownload/GeoIP2-ISP_20140819.tar.gz" -C "/tmp/geoipdownload/"
mv "/tmp/geoipdownload/GeoIP2-ISP_20140819/GeoIP2-ISP.mmdb" "$SRC_DIR/GeoIP2-ISP.mmdb"
curl "https://download.maxmind.com/app/geoip_download?edition_id=GeoIP2-City&date=20160823&suffix=tar.gz&license_key=$1" -o "/tmp/geoipdownload/GeoIP2-City_20160823.tar.gz"
tar zxf "/tmp/geoipdownload/GeoIP2-City_20160823.tar.gz" -C "/tmp/geoipdownload/"
mv "/tmp/geoipdownload/GeoIP2-City_20160823/GeoIP2-City.mmdb" "$SRC_DIR/GeoIP2-City.mmdb"
rm -rf "/tmp/geoipdownload"
