FROM alpine:3.10

MAINTAINER <PERSON>-<PERSON> <<EMAIL>>

COPY install /tmp

RUN apk --no-cache update && \
    apk --no-cache upgrade && \
    apk add --update --no-cache tzdata && \
    cp /usr/share/zoneinfo/Europe/Paris /etc/localtime && \
    echo "Europe/Paris" > /etc/timezone && \
    /tmp/install_geoipupdate.sh && \
    apk del tzdata && \
    rm -rf /var/cache/apk/* /tmp/*

VOLUME /var/log/geoipupdate \
       /etc/geoipupdate/crontab \
       /usr/local/bin/entry.sh \
       /usr/local/etc/GeoIP.conf \
       /usr/local/share/GeoIP

CMD ["/usr/local/bin/entry.sh"]
