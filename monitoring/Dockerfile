FROM alpine:3.4

MAINTAINER <PERSON><PERSON><PERSON> <<EMAIL>>

RUN apk --no-cache update && \
    apk --no-cache upgrade && \
    apk add --update --no-cache python3 python3-dev curl curl-dev tzdata gcc musl-dev && \
    cp /usr/share/zoneinfo/Europe/Paris /etc/localtime && \
    echo "Europe/Paris" > /etc/timezone && \
    pip3 install --upgrade pip && \
    export PYCURL_SSL_LIBRARY=openssl && \
    pip3 install --upgrade six python-dateutil pycurl && \
    apk del python3-dev curl-dev tzdata gcc musl-dev && \
    rm -rf /var/cache/apk/*

ENV MONITORING_LOG_DIR=/var/log \
    MONITORING_CFG_DIR=/etc/druid

WORKDIR /home/<USER>

VOLUME /usr/local/bin/entry.sh \
       /var/log \
       /etc/druid \
       /home/<USER>/logmonitor

CMD ["/usr/local/bin/entry.sh"]
