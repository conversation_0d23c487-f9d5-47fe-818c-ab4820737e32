[COMMON_CONFIG]
debug = False
log-monitoring-config = LOG_MONITORING_CONFIG

[LOG_MONITORING_CONFIG]
mailer-config = LOCAL_MAILER_CONFIG
druid-config = LOCAL_DRUID_CONFIG

[LOCAL_DRUID_CONFIG]
host: druid-broker
port: 8082
data-sources: all-filtered-nginx-log-entries,all-filtered-rtmp-log-entries,all-filtered-wowza-log-entries,all-filtered-vod-viewing-entries,all-filtered-live-viewing-entries
data-sources-max-time: PT5M,PT5M,PT5M,PT5M,PT5M
server-host-data-sources: all-filtered-nginx-log-entries,all-filtered-rtmp-log-entries,all-filtered-wowza-log-entries
server-host-all-filtered-nginx-log-entries:cachecdn7,cachecdn2,cachecdn1,cache2,cachecdn3,dorcel-filer4,cachecdn6,cachecdn5,dorcel-filer5,cachecdn4,dorcel-filer-3
server-host-all-filtered-nginx-log-entries-max-time: PT5M,PT5M,PT5M,PT5M,PT5M,PT5M,PT5M,PT5M,PT5M,PT5M,PT5M
server-host-all-filtered-rtmp-log-entries:zeturf-enc1,eqdencingst1,eqdencingst2
server-host-all-filtered-rtmp-log-entries-max-time:PT12H,PT12H,PT12H
server-host-all-filtered-wowza-log-entries:streamer9,streamer7
server-host-all-filtered-wowza-log-entries-max-time:PT15M,PT15M
[LOCAL_MAILER_CONFIG]
host = mailer-provider
from-display-name = Log Monitoring mailer
from-username = log-monitoring
to-display-name = Log monitoring developer
to-username = <EMAIL>
