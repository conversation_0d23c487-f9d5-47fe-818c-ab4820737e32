#FROM amazoncorretto:8
#
#ARG HOST_GID
#ARG HOST_UID
#ARG kafka_version=2.4.1
#ARG scala_version=2.12
#ARG glibc_version=2.30-r0
#ARG vcs_ref=unspecified
#ARG build_date=unspecified
#
#LABEL org.label-schema.name="kafka" \
#      org.label-schema.description="Apache Kafka" \
#      org.label-schema.build-date="${build_date}" \
#      org.label-schema.vcs-url="https://github.com/wurstmeister/kafka-docker" \
#      org.label-schema.vcs-ref="${vcs_ref}" \
#      org.label-schema.version="${scala_version}_${kafka_version}" \
#      org.label-schema.schema-version="1.0" \
#      maintainer="wurstmeister"
#
#ENV KAFKA_VERSION=$kafka_version \
#    SCALA_VERSION=$scala_version \
#    KAFKA_HOME=/opt/kafka \
#    GLIBC_VERSION=$glibc_version
#
#ENV PATH=${PATH}:${KAFKA_HOME}/bin
#
#COPY download-kafka.sh start-kafka.sh broker-list.sh create-topics.sh versions.sh /tmp/
#
## RUN getent group kafka &>/dev/null || ( \
##       groupadd kafka -g "$HOST_GID" && \
##       useradd -N -G kafka -s /bin/sh kafka -u "$HOST_UID")
#
#RUN yum install -y wget tar gzip vim sudo procps bash curl jq docker \
# && chmod a+x /tmp/*.sh \
# && getent group kafka &>/dev/null || ( \
#      groupadd kafka -g "$HOST_GID" && \
#      useradd -N -G kafka -s /bin/sh kafka -u "$HOST_UID") \
# && mv /tmp/start-kafka.sh /tmp/broker-list.sh /tmp/create-topics.sh /tmp/versions.sh /usr/bin \
# && sync && /tmp/download-kafka.sh \
# && tar xfz /tmp/kafka_${SCALA_VERSION}-${KAFKA_VERSION}.tgz -C /opt \
# && rm /tmp/kafka_${SCALA_VERSION}-${KAFKA_VERSION}.tgz \
# && ln -s /opt/kafka_${SCALA_VERSION}-${KAFKA_VERSION} ${KAFKA_HOME} \
# && rm /tmp/* #\
##  && wget https://github.com/sgerrand/alpine-pkg-glibc/releases/download/${GLIBC_VERSION}/glibc-${GLIBC_VERSION}.apk \
##  && yum install glibc-${GLIBC_VERSION}.apk \
##  && rm glibc-${GLIBC_VERSION}.apk
#
#
#
#COPY overrides /opt/overrides
#
#VOLUME ["/kafka"]
#
## Use "exec" form so that it runs as PID 1 (useful for graceful shutdown)
#CMD ["/usr/local/bin/entry.sh"]
## CMD ["start-kafka.sh"]

########################################################################################################################

FROM openjdk:8u212-jre-alpine

ARG kafka_version=2.4.1
ARG scala_version=2.12
ARG glibc_version=2.30-r0
ARG vcs_ref=unspecified
ARG build_date=unspecified

LABEL org.label-schema.name="kafka" \
      org.label-schema.description="Apache Kafka" \
      org.label-schema.build-date="${build_date}" \
      org.label-schema.vcs-url="https://github.com/wurstmeister/kafka-docker" \
      org.label-schema.vcs-ref="${vcs_ref}" \
      org.label-schema.version="${scala_version}_${kafka_version}" \
      org.label-schema.schema-version="1.0" \
      maintainer="wurstmeister"

ENV KAFKA_VERSION=$kafka_version \
    SCALA_VERSION=$scala_version \
    KAFKA_HOME=/opt/kafka \
    GLIBC_VERSION=$glibc_version

ENV PATH=${PATH}:${KAFKA_HOME}/bin

COPY download-kafka.sh start-kafka.sh broker-list.sh create-topics.sh versions.sh /tmp/

RUN apk add --no-cache bash curl jq docker \
 && chmod a+x /tmp/*.sh \
 && mv /tmp/start-kafka.sh /tmp/broker-list.sh /tmp/create-topics.sh /tmp/versions.sh /usr/bin \
 && sync && /tmp/download-kafka.sh \
 && tar xfz /tmp/kafka_${SCALA_VERSION}-${KAFKA_VERSION}.tgz -C /opt \
 && rm /tmp/kafka_${SCALA_VERSION}-${KAFKA_VERSION}.tgz \
 && ln -s /opt/kafka_${SCALA_VERSION}-${KAFKA_VERSION} ${KAFKA_HOME} \
 && rm /tmp/* \
 && wget https://github.com/sgerrand/alpine-pkg-glibc/releases/download/${GLIBC_VERSION}/glibc-${GLIBC_VERSION}.apk \
 && apk add --no-cache --allow-untrusted glibc-${GLIBC_VERSION}.apk \
 && rm glibc-${GLIBC_VERSION}.apk

COPY overrides /opt/overrides

VOLUME ["/kafka"]

 # Use "exec" form so that it runs as PID 1 (useful for graceful shutdown)
CMD ["start-kafka.sh"]

#####################################################################################################################

#FROM openjdk:8u212-jre-alpine
#
#ARG HOST_GID
#ARG HOST_UID
#
#MAINTAINER Pierre-Alexandre Entraygues <<EMAIL>>
#
#RUN apk add wget tar gzip procps vim sudo bash
#
#ENV SCALA_BINARY_VERSION=2.13 \
#     KAFKA_VERSION=2.4.0 \
#     KAFKA_HOME=/opt/kafka
#
## Install zookeeper/kafka binaries and configuration files.
#RUN mkdir -p /opt && \
#    wget "http://apache.mirrors.ovh.net/ftp.apache.org/dist/kafka/$KAFKA_VERSION/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz" -O /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz && \
#    tar -xzf /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz -C /opt/ && \
#    ln -s /opt/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION $KAFKA_HOME && \
#    rm /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz
#
## Expose kafka port.
#EXPOSE 9092
#
#RUN getent group kafka &>/dev/null || ( \
#    addgroup kafka -g "$HOST_GID" && \
#    adduser -S -G kafka -s /bin/sh kafka -u "$HOST_UID")
#
#VOLUME /var/log \
#       /etc/kafka \
#       /usr/local/bin \
#       /data \
#       /tmp
#
#CMD ["/usr/local/bin/entry.sh"]
