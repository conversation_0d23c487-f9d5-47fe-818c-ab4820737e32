#!/bin/sh -e
KAFKA_BIN_DIR="$KAFKA_HOME/bin"
if [ -z "${FILTER_NUMBER}" ]; then
    "$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-service-entries
else
    FILTER_MAX=$((FILTER_NUMBER - 1))
    for i in $(seq "${FILTER_MAX}"); do
        "$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic "all-service-entries-${i}"
    done
fi
"$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-filtered-nginx-log-entries
"$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-filtered-rtmp-log-entries
"$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-filtered-wowza-log-entries
"$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-filtered-live-viewing-entries
"$KAFKA_BIN_DIR/kafka-topics.sh" --delete --zookeeper zookeeper-server:2181 --topic all-filtered-vod-viewing-entries
