#!/usr/bin/python3

from datetime import date, datetime
import re, sys, os
import argparse
import json


base_dir = "/data1/status/data/"

# Res got one entry for each process
res = {'0' : False, '1': False, '2': <PERSON>alse, '3': <PERSON>als<PERSON>, '4': <PERSON>als<PERSON>, '5': <PERSON>als<PERSON>, '6': <PERSON>als<PERSON>, '7': <PERSON>als<PERSON>, '8': <PERSON>als<PERSON>, '9': <PERSON>als<PERSON>, '10': False, '11': <PERSON>alse, '12': False, '13': False, '14': False, '15': False}


def main():

    processes_done = 0 # Processes where the last consume minute has already been found
    today = str(date.today()).split('-') # Get the Date
    directory = base_dir + today[0] + "/" + today[1] + "/" + today[2] # Get today's Directory
    pattern = re.compile(r'.*\.status$') # Create pattern to only get the status files
    entries = []

    try:
        entries = [os.path.join(directory, f) for f in os.listdir(directory)]
        entries.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    except:
        print("An error occured, maybe the path does not exist") # It means an error occured, most likely to be the base_dir path that does not exists

    for entry in entries:
        if processes_done == 16:
            break
        files = [os.path.join(entry, f) for f in os.listdir(entry)]
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        for file in files:
            if processes_done < 16:
                if os.path.isfile(file) and pattern.match(file):
                    with open(file, 'r') as contents:
                        contents = contents.read()
                        if contents == "done":
                            if res[file.split('.log.status')[0].split('.')[1]] == False:
                                res[file.split('.log.status')[0].split('.')[1]] = transform_file_to_date(file)
                                processes_done += 1
            else:
                break

    json_to_print = (json.dumps(res, indent=4))  # Return a JSON where each process get a date value corresponding to its last minute consumed
    print(json_to_print.replace("-", "_"))


def transform_file_to_date(filename: str):
    date_part = filename.split(base_dir)[1].split(".")[0]
    datetime_obj = datetime.strptime(date_part, "%Y/%m/%d/%H/%M")
    now = datetime.now()
    return int((now - datetime_obj).total_seconds() / 60)


if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='A script with an optional -d option')
    parser.add_argument('-d', '--debug', action='store_true', help='Print the items')
    args = parser.parse_args()
    if args.debug:
        debug_print = []
        for key in res.keys():
            key = key.replace("-", "_")
            debug_print.append({"{#ITEM}": key})
        print(json.dumps(debug_print, indent=4))
    else:
        main()
