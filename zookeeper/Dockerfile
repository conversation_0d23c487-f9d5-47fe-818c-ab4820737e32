FROM openjdk:8-jre-alpine

MAINTAINER <PERSON>-<PERSON> <<EMAIL>>

RUN apk --no-cache update && \
    apk --no-cache upgrade && \
    apk add --update --no-cache tzdata bash sudo && \
    cp /usr/share/zoneinfo/Europe/Paris /etc/localtime && \
    echo "Europe/Paris" > /etc/timezone && \
    apk del tzdata && \
    rm -rf /var/cache/apk/* /tmp/*

ENV SCALA_BINARY_VERSION=2.12 \
    KAFKA_VERSION=2.3.0 \
    KAFKA_HOME=/opt/kafka

# Install zookeeper/kafka binaries and configuration files.
RUN mkdir -p /opt && \
    wget "http://apache.mirrors.ovh.net/ftp.apache.org/dist/kafka/$KAFKA_VERSION/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz" -O /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz && \
    tar -xzf /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz -C /opt/ && \
    ln -s /opt/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION $KAFKA_HOME && \
    rm /tmp/kafka_$SCALA_BINARY_VERSION-$KAFKA_VERSION.tgz

# Expose zookeeper port.
EXPOSE 2181

VOLUME /var/log \
       /etc/zookeeper \
       /usr/local/bin \
       /data \
       /tmp

CMD ["/usr/local/bin/entry.sh"]
