# ZOO KEEPER MONITORING

To monitor and control the zookeeper the you have to use #ZooKeeper Commands: The Four Letter Words.

doc : https://zookeeper.apache.org/doc/r3.1.2/zookeeperAdmin.html#sc_zkCommands

It is sets in the global Docker-compse.yml of logs-analytics; it is enabled in the zookeeper section with : # ZOO_4LW_COMMANDS_WHITELIST: "*"
in the environement variables.

To use this option you have lots of option : 

- # dump 
Lists the outstanding sessions and ephemeral nodes. This only works on the leader.

- # envi
Print details about serving environment

- # kill 
Shuts down the server. This must be issued from the machine the ZooKeeper server is running on.

- # reqs
List outstanding requests

- # ruok
Tests if server is running in a non-error state. The server will respond with imok if it is running. Otherwise it will not respond at all.

- # srst
Reset statistics returned by stat command.

- # stat
Lists statistics about performance and connected clients.

- # srvr
Lists informations about the server zookeeper.

The rest of the available command is in the link at the top of the ReadMe.

# How it works

To use these options see the exemple below : 

# echo ```option``` | nc 127.0.0.1 2181

for exemple : 
 
```INPUT``` echo ruok | nc 127.0.0.1 2181 

```OUTPUT``` imok (if it's ok) / (nothing if it's not).
