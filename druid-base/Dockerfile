FROM amazoncorretto:8

MAINTAIN<PERSON> <PERSON><PERSON><PERSON> <<EMAIL>>

ENV DRUID_VERSION=0.16.0 \
    DRUID_HOME=/opt/druid \
    DRUID_CFG=/etc/druid \
    DRUID_TMP=/data/druid/tmp

RUN yum install -y wget tar gzip procps vim

COPY bin_files/node.sh /tmp/node.sh

# Install druid binaries and extensions.
RUN mkdir -p /etc/druid && \
    mkdir -p /opt && \
    yum install -y wget tar gzip mysql-connector-java && \
    wget "http://apache.crihan.fr/dist/incubator/druid/$DRUID_VERSION-incubating/apache-druid-$DRUID_VERSION-incubating-bin.tar.gz" -O /tmp/druid-$DRUID_VERSION-bin.tar.gz && \
    tar -xzf /tmp/druid-${DRUID_VERSION}-bin.tar.gz -C /opt && \
    ln -s /opt/apache-druid-${DRUID_VERSION}-incubating $DRUID_HOME && \
    rm /tmp/druid-$DRUID_VERSION-bin.tar.gz && \
    cp /usr/share/java/mysql-connector-java.jar /opt/apache-druid-0.16.0-incubating/extensions/mysql-metadata-storage/

RUN mv /tmp/node.sh /opt/druid/bin/node.sh && \
    chmod +x /opt/druid/bin/node.sh


# wget http://central.maven.org/maven2/mysql/mysql-connector-java/5.1.48/mysql-connector-java-5.1.48.jar -P /opt/apache-druid-${DRUID_VERSION}-incubating/extensions/mysql-metadata-storage/


# opt/druid/bin/node.sh

WORKDIR /opt/druid

VOLUME /etc/druid/_common \
       /var/log/druid \
       /usr/local/bin

CMD ["/usr/local/bin/start.sh"]
