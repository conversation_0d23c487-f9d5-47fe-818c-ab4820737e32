version: '3'

services:
  is_kafka_available:
    image: busybox
    healthcheck:
      test: ["CMD", "nc", "-z", "kafkahost", "9092"]
      interval: 15s
      timeout: 5s
      retries: 100
    command: ["sh", "-c", "sleep infinity"]  # Keeps the container running
    network_mode: "host"
  
  redis:
    network_mode: "host"
    build:
      context: ./redis
    tty: true

  filter-0:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=0
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-0
    container_name: ${BASE_NAME}_filter-0
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-1:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=1
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-1
    container_name: ${BASE_NAME}_filter-1
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-2:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=2
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-2
    container_name: ${BASE_NAME}_filter-2
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-3:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=3
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-3
    container_name: ${BASE_NAME}_filter-3
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-4:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=4
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-4
    container_name: ${BASE_NAME}_filter-4
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-5:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=5
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-5
    container_name: ${BASE_NAME}_filter-5
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-6:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=6
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-6
    container_name: ${BASE_NAME}_filter-6
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped

  filter-7:
    build:
      context: ./filter
      args:
        - HOST_GID=1000
        - HOST_UID=1000
    image: ${BASE_NAME}/filter
    depends_on:
      is_kafka_available:
        condition: service_healthy
    env_file: docker-compose.env
    environment:
      - FILTER_ID=1
      - SCALE_NB=7
      - TZ=UTC
      - CONTAINER_NAME=${BASE_NAME}_filter-7
    container_name: ${BASE_NAME}_filter-7
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./filter/status:/var/status
      - ./filter/bin/default:/root
      - ./filter/log/filter:/var/log
      - ./filter/src/kafkalogfilter:/opt/filter
      - ./filter/src/Hexaparser:/opt/filter/Hexaparser
      - ./filter/etc/${CONFIG_NAME}:/etc/filter
      - ./filter/bin:/usr/local/bin
      - ${UA_DATABASE_DIR}/filter:/usr/local/share/udger/database
      - ${GEOIP_DOWNLOAD_DIR}:/usr/local/share/GeoIP:ro
    restart: unless-stopped
