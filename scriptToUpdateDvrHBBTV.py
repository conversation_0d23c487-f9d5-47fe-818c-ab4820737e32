import xml.etree.ElementTree as ET
import requests
from datetime import datetime, timezone

# The dictionary that will store the correspondance between the segment id and the time it was encoded
id_time_correspondance = {}


### !!! FETCH THE XML DATA FROM THE URL !!! ###
url = 'https://startover-towercast-channel-001.hexaglobe.net/get-stream/prod/manifest.mpd'
response = requests.get(url)
if response.status_code == 200:
    xml_data = response.content
    root = ET.fromstring(xml_data)
else:
    print(f"Failed to fetch data from {url}. Status code: {response.status_code}")
init_key = root[1][1][0][0].attrib['initialization'].split('_')[1]



### !!! LOOP TO GET ALL QUALITIES AND HEIGHTS AND CONTENT TYPES !!! ###
qualities_height = {}
content_type_index = []
index_elem = 0
content_index = 0
for content_type in root[1]:
    try:
        qualities_height[content_type.attrib['contentType']] = []
        content_type_index.append(content_index)
        content_index += 1
        for quality in root[1][index_elem]:
            if content_type.attrib['contentType'] == "audio":
                qualities_height[content_type.attrib['contentType']].append(quality.attrib['bandwidth'])
            else:
                qualities_height[content_type.attrib['contentType']].append(quality.attrib['height'])
    except:
        content_index += 1
        pass
    index_elem += 1



### !!! GET THE TIMESTAMP OF THE INIT KEY AND CHECK IF ALREADY EXISTS !!! ###
dt_object = datetime.strptime(init_key, "%Y%m%d%H%M")
timestamp = dt_object.replace(tzinfo=timezone.utc).timestamp()
if init_key not in id_time_correspondance:
    id_time_correspondance[init_key] = {}



### !!! MAIN LOOP TO FILL THE CORRESPONDANCE TABLE !!! ###
content_index = 0
for content_type in qualities_height:

    timeline_index = 0
    quality_index = 0
    last_calculated_time = timestamp
    x = 1

    for quality in qualities_height[content_type]:
        x = 1

        if content_type not in id_time_correspondance[init_key]:
            id_time_correspondance[init_key][content_type] = {}
        for quality in qualities_height[content_type]:
            if quality_index not in id_time_correspondance[init_key][content_type]:
                id_time_correspondance[init_key][content_type][quality_index] = {}

        try:
            tmp = root[1][content_type_index[content_index]][quality_index][timeline_index][0]
        except:    
            timeline_index = 1

        # IF NEW INIT KEY OR IF FIRST LINE T=0 ELSE WE HAVE TO CALCULATE THE TIME TO ADD THE NEW LINE TO THE CORRESPONDANCE TABLE
        timescale = int(root[1][content_type_index[content_index]][quality_index][timeline_index].attrib['timescale'])

        # This first if is to completely re-create the correspondance table because the curl returns the entire segment timeline
        if (root[1][content_type_index[content_index]][quality_index][timeline_index][0][0].attrib['t'] == '0'):
            for elem in root[1][content_type_index[content_index]][quality_index][timeline_index][0]:
                repeat = 1
                if 'r' in elem.attrib:
                    repeat += int(elem.attrib['r'])
                for i in range(0, repeat):
                    if x == 1:
                        new_time = round(timestamp, 2)
                    else:
                        new_time = round(last_calculated_time + (float(elem.attrib['d']) / timescale), 2)
                    id_time_correspondance[init_key][content_type][quality_index][x] = new_time
                    last_calculated_time = new_time
                    x += 1
            # quality_index += 1
        # This else is to add the new segment timeline to the correspondance table when curled timeline is not complete and we miss first segments informations
        else:
            # 3 cas : il manque tout car cest un nouveau init key ducou on invente le debut puis on rempli correctement la suite
            # 2eme cas : on a un curled_start_number qui est deja dans notre dict donc on recalcul seulement tout ce qui le suit
            # 3eme cas : on a un curled_start_number qui nest pas dans notre dict donc on doit completer les trous jusqu'a ce nombre puis on ajoute le reste
            parts = root[1][content_type_index[content_index]][quality_index][timeline_index].attrib['media'].split('.')[0].split('_')
            default_segment_duration = int(parts[len(parts) - 1]) / 10**6
            curled_start_number = int(root[1][content_type_index[content_index]][quality_index][timeline_index].attrib['startNumber'])
            start_new_array_index = len(id_time_correspondance[init_key][content_type][quality_index])  # Ce quon a deja ete capable de remplir (si new c'est empty)
            tmp_new_dict = {}
            if start_new_array_index == 0:  # If we never calculated anything for this encoding time / quality, we start to fill from scratch using default segment durations
                start_new_array_index = 1
            else:                           # Else, it means we already calculated some segments, so we have to calculate the missing ones, so we have to start after the last calculated time
                last_calculated_time = id_time_correspondance[init_key][content_type][quality_index][start_new_array_index - 1]
            if curled_start_number > start_new_array_index:    # It means we have a gap between the last calculated time and the first segment time in the curled timeline so we have to fill it using default segment durations
                for i in range(start_new_array_index, curled_start_number):
                    if i == 1:
                        new_time = round(timestamp, 2)
                    else:
                        new_time = round(last_calculated_time + (default_segment_duration), 2)
                    tmp_new_dict[i] = new_time
                    last_calculated_time = new_time
                id_time_correspondance[init_key][content_type][quality_index].update(tmp_new_dict)  # We update the correspondance table with the missing segments we just calculated
            x = curled_start_number
            for elem in root[1][content_type_index[content_index]][quality_index][timeline_index][0]:  # Here we calculate the rest of the segments (after the gap we just filled using default segment durations OR if there was no gap, we just calculate (maybe recalculate some at the end of know dict) the rest of the segments after the start number of the curled timeline)
                repeat = 1
                if 'r' in elem.attrib:
                    repeat += int(elem.attrib['r'])
                for i in range(0, repeat):
                    if x == curled_start_number:
                        new_time = round(int(elem.attrib['t']) / timescale + timestamp, 2)
                    else:
                        new_time = round(last_calculated_time + (float(elem.attrib['d']) / timescale), 2)
                    tmp_new_dict[x] = new_time
                    last_calculated_time = new_time
                    x += 1
            id_time_correspondance[init_key][content_type][quality_index].update(tmp_new_dict)

        quality_index += 1
    content_index += 1

# print(id_time_correspondance)
