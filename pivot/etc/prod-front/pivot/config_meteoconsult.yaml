# The port on which the Pivot server will listen on
port: 9093

clusters:
  - name: druid

    # The database type of the cluster
    type: druid

    # The host (hostname:port) of the cluster. In the Druid case this must be the broker.
    host: druid-broker:8082

    # The timeout to set on the queries in ms. Default is <code>40000</code>
    #timeout: 40000 # <- default

    # Should the sources of this cluster be automatically scanned and new
    # sources added as data cubes. Default: <code>auto</code>
    sourceListScan: disable

    # Should the list of sources be reloaded every time that Pivot is
    # loaded. This will put additional load on the data store but will ensure that
    # sources are visible in the UI as soon as they are created.
    sourceListRefreshOnLoad: false

    # How often should sources be reloaded in ms. Default: <code>15000</code>
    sourceListRefreshInterval: 0

    # Should sources be scanned for additional dimensions every time that
    # Pivot is loaded. This will put additional load on the data store but will
    # ensure that dimension are visible in the UI as soon as they are created. Default: <code>120000</code>
    sourceReintrospectOnLoad: false # <- default

    # How often should source schema be reloaded in ms.
    sourceReintrospectInterval: 0

    # Database specific (druid) ===============

    # The introspection strategy for the Druid external.
    #introspectionStrategy: segment-metadata-fallback # <- default

dataCubes:
  - name: all-filtered-nginx-log-entries
    title: All filtered nginx log entries
    clusterName: druid
    source: all-filtered-nginx-log-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $service_id.is('meteoconsult-live-live') and $http_via.isnt('') and $remote_addr.match('^(195.178.113.|************)').not()

    options:
      customAggregations:
        stream_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'stream_time_ms']
            fnAggregate: "function(current, __time, stream_time_ms) {
              if(stream_time_ms !== null){
                return current + (__time - stream_time_ms);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        expire_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'secure_link_expires']
            fnAggregate: "function(current, __time, secure_link_expires) {
              if(secure_link_expires !== null){
                return current + (__time - secure_link_expires*1e3);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        stream_index:
          aggregation:
            type: javascript
            fieldNames: ['stream_index']
            fnAggregate: "function(current, stream_index) {
              if(stream_index !== null){
                return current + stream_index*1;
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        unique_ip:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_via","http_x_forwarded_for"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_user_agent","http_via","http_x_forwarded_for"]
            byRow: true
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

      - name: status
        type: NUMBER

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # kind: string
      # ^ (optional) the kind of the dimension. Can be 'string', 'time', 'number', or 'boolean'. Defaults to 'string'
      #
      # formula: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here
      #   See also the expressions API reference: https://plywood.imply.io/expressions
      #
      # url: string
      # ^ (optional) a url (including protocol) associated with the dimension, with optional token '%s'
      #   that is replaced by the dimension value to generate links specific to each value.

      - name: service_id
        title: Service id

      - name: server_addr
        title: Server addr

      - name: server_protocol
        title: Server protocol

      - name: host
        title: Host

      - name: status
        title: Status

      - name: request_uri
        title: Request uri

      - name: request_method
        title: Request method

      - name: request_completion
        title: Request completion

      - name: upstream_addr
        title: Upstream addr

      - name: upstream_cache_status
        title: Upstream cache status

      - name: upstream_status
        title: Upstream status

      - name: secure_link
        title: Secure link

      - name: extension
        title: Extension

      - name: language
        title: Language

      - name: quality
        title: Quality

      - name: remote_addr
        title: Remote address

      - name: http_referer
        title: Http referer

      - name: http_accept_language
        title: Http accept language

      - name: http_x_forwarded_for
        title: Http x forwarded for

      - name: http_via
        title: Http via

      - name: http_user_agent
        title: Http user agent

      - name: os_name
        title: OS name

      - name: os_family
        title: OS family

      - name: ua_name
        title: User agent name

      - name: ua_family
        title: User agent family

      - name: device_type
        title: Device type

      - name: browser_type
        title: Browser type

      - name: secure_link_expires
        title: Secure link expires (s)

      - name: stream_time_ms
        title: Stream time (ms)

      - name: stream_index
        title: Stream index

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: bytes_sent
        title: Bytes Sent (bytes)
        formula: $main.sum($bytes_sent)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)

      - name: duration_s
        title: Duration (s)
        formula: $main.sum($duration_s)

      - name: duration_m
        title: Duration (m)
        formula: $main.sum($duration_s) / 60

      - name: duration_h
        title: Duration (h)
        formula: $main.sum($duration_s) / 3600

      - name: body_bytes_sent
        title: Body Bytes Sent (bytes)
        formula: $main.sum($body_bytes_sent)

      - name: unique_ips
        title: Unique viewers (IP)
        formula: $main.custom(unique_ip)

      - name: request_time
        title: Request time (s)
        formula: $main.sum($request_time)

      - name: avg_request_time
        title: Average request time (s)
        formula: $main.sum($request_time) / $main.sum($count)

      - name: upstream_response_time
        title: Upstream response time (s)
        formula: $main.sum($upstream_response_time)

      - name: avg_upstream_response_time
        title: Average upstream response time (s)
        formula: $main.sum($upstream_response_time) / $main.sum($count)

      - name: bitrate_streaming
        title: Bitrate streaming (bits/s) - minutely
        formula: $main.sum($bytes_sent) * 8 / 60

      - name: upstream_bitrate_streaming
        title: Upstream bitrate streaming (bits/s) - minutely
        formula: $main.sum($upstream_response_length) * 8 / 60

      - name: request_length
        title: Request length (bytes)
        formula: $main.sum($request_length)

      - name: avg_request_length
        title: Average request length (bytes)
        formula: $main.sum($request_length) / $main.sum($count)

      - name: upstream_response_length
        title: Upstream response length (bytes)
        formula: $main.sum($upstream_response_length)

      - name: avg_stream_delay_s
        title: Average stream delay (s)
        formula: $main.filter($extension=='ts').custom(stream_delay_ms) / (1000 * $main.filter($extension=='ts').sum($count))

      - name: avg_expire_delay_s
        title: Average expire delay (m)
        formula: $main.custom(expire_delay_ms) / (60000 * $main.sum($count))

      - name: avg_stream_index
        title: Average stream index
        formula: $main.custom(stream_index) / $main.sum($count)

      - name: main_playlist_start
        title: Main playlist start
        formula: $main.filter($extension=='m3u8' and $quality=='' and $status==200).sum($count)
