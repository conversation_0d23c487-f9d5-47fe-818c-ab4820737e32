# The port on which the Pivot server will listen on
port: 9090

clusters:
  - name: druid

    # The database type of the cluster
    type: druid

    # The host (hostname:port) of the cluster. In the Druid case this must be the broker.
    host: druid-broker:8082

    # The timeout to set on the queries in ms. Default is <code>40000</code>
    #timeout: 40000 # <- default

    # Should the sources of this cluster be automatically scanned and new
    # sources added as data cubes. Default: <code>auto</code>
    sourceListScan: disable

    # Should the list of sources be reloaded every time that Pivot is
    # loaded. This will put additional load on the data store but will ensure that
    # sources are visible in the UI as soon as they are created.
    sourceListRefreshOnLoad: false

    # How often should sources be reloaded in ms. Default: <code>15000</code>
    sourceListRefreshInterval: 0

    # Should sources be scanned for additional dimensions every time that
    # Pivot is loaded. This will put additional load on the data store but will
    # ensure that dimension are visible in the UI as soon as they are created. Default: <code>120000</code>
    sourceReintrospectOnLoad: false # <- default

    # How often should source schema be reloaded in ms.
    sourceReintrospectInterval: 0

    # Database specific (druid) ===============

    # The introspection strategy for the Druid external.
    #introspectionStrategy: segment-metadata-fallback # <- default

dataCubes:
  - name: all-filtered-nginx-log-entries
    title: All filtered nginx log entries
    clusterName: druid
    source: all-filtered-nginx-log-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $remote_addr.match('^195.178.113.').not()

    options:
      customAggregations:
        unique_medias_custom:
          aggregation:
            type: hyperUnique
            fieldName: unique_media_id
        unique_users:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_via","http_x_forwarded_for"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_user_agent","http_via","http_x_forwarded_for"]
            byRow: true
        stream_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'stream_time_ms']
            fnAggregate: "function(current, __time, stream_time_ms) {
              if(stream_time_ms !== null){
                return current + (__time - stream_time_ms);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        expire_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'secure_link_expires']
            fnAggregate: "function(current, __time, secure_link_expires) {
              if(secure_link_expires !== null){
                return current + (__time - secure_link_expires*1e3);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        stream_index:
          aggregation:
            type: javascript
            fieldNames: ['stream_index']
            fnAggregate: "function(current, stream_index) {
              if(stream_index !== null){
                return current + stream_index*1;
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

      - name: stream_time_ms
        type: NUMBER

      - name: stream_index
        type: NUMBER

      - name: status
        type: NUMBER

      - name: isp_asn
        type: NUMBER

      - name: secure_link_expires
        type: NUMBER

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # kind: string
      # ^ (optional) the kind of the dimension. Can be 'string', 'time', 'number', or 'boolean'. Defaults to 'string'
      #
      # formula: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here
      #   See also the expressions API reference: https://plywood.imply.io/expressions
      #
      # url: string
      # ^ (optional) a url (including protocol) associated with the dimension, with optional token '%s'
      #   that is replaced by the dimension value to generate links specific to each value.

      - name: service_id
        title: Service id

      - name: playlist_id
        title: Playlist id

      - name: fromhost
        title: From host

      - name: fromhost_ip
        title: From host ip

      - name: stream_time_ms
        title: Stream time (ms)

      - name: stream_index
        title: Stream index

      - name: server_addr
        title: Server addr

      - name: server_protocol
        title: Server protocol

      - name: host
        title: Host

      - name: status
        title: Status

      - name: request_uri
        title: Request uri

      - name: request_filename
        title: Request filename

      - name: request_method
        title: Request method

      - name: https
        title: Https

      - name: request_completion
        title: Request completion

      - name: upstream_addr
        title: Upstream addr

      - name: upstream_cache_status
        title: Upstream cache status

      - name: upstream_status
        title: Upstream status

      - name: secure_link
        title: Secure link

      - name: extension
        title: Extension

      - name: language
        title: Language

      - name: quality
        title: Quality

      - name: media_id
        title: Media id

      - name: isp_asn
        title: Isp asn

      - name: os_name
        title: OS name

      - name: os_family
        title: OS family

      - name: ua_name
        title: User agent name

      - name: ua_family
        title: User agent family

      - name: device_type
        title: Device type

      - name: browser_type
        title: Browser type

      - name: remote_addr
        title: Remote addr (debug)

      - name: http_referer
        title: Http referer (debug)

      - name: http_x_playback_session_id
        title: Http x playback session id (debug)

      - name: http_accept_language
        title: Http accept language (debug)

      - name: http_x_forwarded_for
        title: Http x forwarded for (debug)

      - name: http_via
        title: Http via (debug)

      - name: http_user_agent
        title: Http user agent (debug)

      - name: secure_link_expires
        title: Secure link expires (s) (debug)

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: body_bytes_sent
        title: Body Bytes Sent (bytes)
        formula: $main.sum($body_bytes_sent)

      - name: bytes_sent
        title: Bytes Sent (bytes)
        formula: $main.sum($bytes_sent)

      - name: bitrate_streaming
        title: Bitrate streaming (bits/s)
        formula: $main.sum($bytes_sent) * 8 / 60

      - name: upstream_bitrate_streaming
        title: Upstream bitrate streaming (bits/s)
        formula: $main.sum($upstream_response_length) * 8 / 60

      - name: request_length
        title: Request Length (bytes)
        formula: $main.sum($request_length)

      - name: avg_request_length
        title: Average request Length (bytes)
        formula: $main.sum($request_length) / $main.sum($count)

      - name: request_time
        title: Request Time (s)
        formula: $main.sum($request_time)

      - name: avg_request_time
        title: Average request Time (s)
        formula: $main.sum($request_time) / $main.sum($count)

      - name: upstream_response_time
        title: Upstream response Time (s)
        formula: $main.sum($upstream_response_time)

      - name: avg_upstream_response_time
        title: Average upstream response Time (s)
        formula: $main.sum($upstream_response_time) / $main.sum($count)

      - name: upstream_response_length
        title: Upstream response Length (bytes)
        formula: $main.sum($upstream_response_length)

      - name: duration_s
        title: Duration (s)
        formula: $main.sum($duration_s)

      - name: avg_stream_delay_ms
        title: Average stream delay (ms)
        formula: $main.custom(stream_delay_ms) / $main.sum($count)

      - name: avg_expire_delay_ms
        title: Average expire delay (ms)
        formula: $main.custom(expire_delay_ms) / $main.sum($count)

      - name: unique_ips
        formula: $main.countDistinct($remote_addr)

      - name: avg_stream_index
        title: Average stream index
        formula: $main.custom(stream_index) / $main.sum($count)

      - name: audience
        title: Audience
        formula: $main.sum($duration_s) / 60

      - name: download_bitrate
        title: Download bitrate (bit/s)
        formula: $main.sum($bytes_sent) * 8 / $main.sum($request_time)

      - name: nb_medias
        title: Number of medias
        formula: $main.custom(unique_medias_custom)

      - name: unique_users
        title: Unique users (IP)
        formula: $main.custom(unique_users)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)

      - name: main_playlist_start
        title: Main playlist start
        formula: $main.filter($extension=='m3u8' and $quality=='' and $status==200).sum($count)


  - name: all-filtered-wowza-log-entries
    title: All filtered wowza log entries
    clusterName: druid
    source: all-filtered-wowza-log-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $c_ip.match('^195.178.113.').not()

    options:
      customAggregations:
        unique_users:
          aggregation:
            type: cardinality
            fieldNames: ["c_ip"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["c_ip","c_user_agent"]
            byRow: true
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

      - name: isp_asn
        type: NUMBER

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # kind: string
      # ^ (optional) the kind of the dimension. Can be 'string', 'time', 'number', or 'boolean'. Defaults to 'string'
      #
      # formula: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here
      #   See also the expressions API reference: https://plywood.imply.io/expressions
      #
      # url: string
      # ^ (optional) a url (including protocol) associated with the dimension, with optional token '%s'
      #   that is replaced by the dimension value to generate links specific to each value.

      - name: service_id
        title: Service id

      - name: playlist_id
        title: Playlist id

      - name: fromhost
        title: From host

      - name: fromhost_ip
        title: From host ip

      - name: x_event
        title: Event

      - name: x_category
        title: Category

      - name: x_severity
        title: Severity

      - name: x_status
        title: Status

      - name: x_ctx
        title: Ctx

      - name: x_comment
        title: Comment

      - name: x_vhost
        title: Vhost

      - name: x_app
        title: App

      - name: x_appinst
        title: Appinst

      - name: s_ip
        title: Server ip

      - name: s_port
        title: Server port

      - name: s_uri
        title: Server uri

      - name: c_ip
        title: Client ip

      - name: c_proto
        title: Proto

      - name: c_referrer
        title: Referrer

      - name: c_user_agent
        title: User agent

      - name: c_client_id
        title: Client id

      - name: x_stream_id
        title: Stream id

      - name: x_spos
        title: Spos

      - name: x_sname
        title: Sname

      - name: x_sname_query
        title: Sname query

      - name: x_file_name
        title: File name

      - name: x_file_ext
        title: File ext

      - name: x_file_size
        title: File size

      - name: x_file_length
        title: File length

      - name: x_suri
        title: S uri

      - name: x_suri_stem
        title: Suri stem

      - name: x_suri_query
        title: Suri query

      - name: cs_uri_stem
        title: Uri stem

      - name: cs_uri_query
        title: Uri query

      - name: language
        title: Language

      - name: quality
        title: Quality

      - name: media_id
        title: Media id

      - name: isp_asn
        title: Isp asn

      - name: os_name
        title: OS name

      - name: os_family
        title: OS family

      - name: ua_name
        title: User agent name

      - name: ua_family
        title: User agent family

      - name: device_type
        title: Device type

      - name: browser_type
        title: Browser type

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: cs_bytes
        title: Cs bytes (bytes)

      - name: sc_bytes
        title: Sc bytes (bytes)

      - name: cs_stream_bytes
        title: Cs stream bytes (bytes)

      - name: sc_stream_bytes
        title: Sc stream bytes (bytes)

      - name: x_duration
        title: Duration (s)

      - name: unique_users
        title: Unique users (IP)
        formula: $main.custom(unique_users)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)

      - name: unique_ips
        formula: $main.countDistinct($c_ip)


  - name: all-filtered-live-viewing-entries
    title: All filtered live viewing entries
    clusterName: druid
    source: all-filtered-live-viewing-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $remote_addr.match('^195.178.113.').not()

    options:
      customAggregations:
        unique_viewings_custom:
          aggregation:
            type: hyperUnique
            fieldName: nb_viewings
        unique_sessions_custom:
          aggregation:
            type: hyperUnique
            fieldName: nb_sessions
        unique_medias_custom:
          aggregation:
            type: hyperUnique
            fieldName: unique_media_id
        unique_users:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_via","http_x_forwarded_for"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_user_agent","http_via","http_x_forwarded_for"]
            byRow: true
        expire_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'secure_link_expires']
            fnAggregate: "function(current, __time, secure_link_expires) {
              if(secure_link_expires !== null){
                return current + (__time - secure_link_expires*1e3);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

      - name: city_geoname_id
        type: NUMBER

      - name: continent_geoname_id
        type: NUMBER

      - name: country_geoname_id
        type: NUMBER

      - name: subdiv_1_geoname_id
        type: NUMBER

      - name: isp_asn
        type: NUMBER

      - name: session_id
        type: NUMBER

      - name: session_duration_class_s
        type: NUMBER

      - name: session_start
        type: NUMBER

      - name: session_stream_start
        type: NUMBER

      - name: session_stop
        type: NUMBER

      - name: session_stream_stop
        type: NUMBER

      - name: viewing_id
        type: NUMBER

      - name: viewing_duration_class_s
        type: NUMBER

      - name: viewing_start
        type: NUMBER

      - name: viewing_stream_start
        type: NUMBER

      - name: viewing_stop
        type: NUMBER

      - name: viewing_stream_stop
        type: NUMBER

      - name: secure_link_expires
        type: NUMBER

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # type: STRING
      # ^ (optional) the Plywood type of the dimension. Defaults to STRING
      #
      # expression: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here

      - name: service_id
        title: Service id

      - name: playlist_id
        title: Playlist id

      - name: fromhost
        title: From host

      - name: fromhost_ip
        title: From host ip

      - name: remote_addr
        title: Remote addr

      - name: server_addr
        title: Server addr

      - name: server_protocol
        title: Server protocol

      - name: host
        title: Host

      - name: http_referer
        title: Http referer

      - name: http_accept_language
        title: Http accept language

      - name: http_x_forwarded_for
        title: Http x forwarded for

      - name: http_via
        title: Http via

      - name: http_user_agent
        title: Http user agent

      - name: city_geoname_id
        title: City geoname id

      - name: continent_geoname_id
        title: Continent geoname id

      - name: country_geoname_id
        title: Country geoname id

      - name: metro_code
        title: Metro code

      - name: time_zone
        title: Time zone

      - name: postal_code
        title: Postal code

      - name: subdiv_1_geoname_id
        title: Subdiv 1 geoname id

      - name: isp_asn
        title: ISP ASN

      - name: os_name
        title: OS name

      - name: os_family
        title: OS family

      - name: ua_name
        title: User agent name

      - name: ua_family
        title: User agent family

      - name: device_type
        title: Device type

      - name: browser_type
        title: Browser type

      - name: session_id
        title: Session id

      - name: session_duration_class_s
        title: Session duration class (s)

      - name: session_start
        title: Session start

      - name: session_stream_start
        title: Session stream start

      - name: session_stop
        title: Session stop

      - name: session_stream_stop
        title: Session stream stop

      - name: viewing_id
        title: Viewing id

      - name: viewing_duration_class_s
        title: Viewing duration class (s)

      - name: viewing_start
        title: Viewing start

      - name: viewing_stream_start
        title: Viewing stream start

      - name: viewing_stop
        title: Viewing stop

      - name: viewing_stream_stop
        title: Viewing stream stop

      - name: secure_link_expires
        title: Secure link expires (s)

      - name: language
        title: Language

      - name: quality
        title: Quality

      - name: media_id
        title: Media id

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: total_viewing_request_count
        title: Total viewing request count
        formula: $main.sum($viewing_total_request_count)

      - name: total_session_request_count
        title: Total session request count
        formula: $main.sum($session_total_request_count)

      - name: total_viewing_bytes_sent
        title: Total viewing bytes sent (byte)
        formula: $main.sum($viewing_total_bytes_sent)

      - name: total_session_bytes_sent
        title: Total session bytes sent (byte)
        formula: $main.sum($session_total_bytes_sent)

      - name: viewing_duration_s
        title: Viewing duration (s)
        formula: $main.sum($viewing_duration_s)

      - name: session_duration_s
        title: Session duration (s)
        formula: $main.sum($session_duration_s)

      - name: nb_viewings
        title: Number of viewings
        formula: $main.custom(unique_viewings_custom)

      - name: nb_sessions
        title: Number of sessions
        formula: $main.custom(unique_sessions_custom)

      - name: nb_medias
        title: Number of medias
        formula: $main.custom(unique_medias_custom)

      - name: avg_expire_delay_ms
        title: Average expire delay (ms)
        formula: $main.custom(expire_delay_ms) / $main.sum($count)

      - name: unique_viewings
        formula: $main.countDistinct($viewing_id)

      - name: unique_sessions
        formula: $main.countDistinct($session_id)

      - name: unique_medias
        formula: $main.countDistinct($media_id)

      - name: unique_ips
        formula: $main.countDistinct($remote_addr)

      - name: unique_users
        title: Unique users (IP)
        formula: $main.custom(unique_users)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)


  - name: all-filtered-vod-viewing-entries
    title: All filtered vod viewing entries
    clusterName: druid
    source: all-filtered-vod-viewing-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $remote_addr.match('^195.178.113.').not()

    options:
      customAggregations:
        unique_viewings_custom:
          aggregation:
            type: hyperUnique
            fieldName: nb_viewings
        unique_sessions_custom:
          aggregation:
            type: hyperUnique
            fieldName: nb_sessions
        unique_medias_custom:
          aggregation:
            type: hyperUnique
            fieldName: unique_media_id
        unique_users:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_via","http_x_forwarded_for"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","http_user_agent","http_via","http_x_forwarded_for"]
            byRow: true
        expire_delay_ms:
          aggregation:
            type: javascript
            fieldNames: ['__time', 'secure_link_expires']
            fnAggregate: "function(current, __time, secure_link_expires) {
              if(secure_link_expires !== null){
                return current + (__time - secure_link_expires*1e3);
              }
              return current;
            }"
            fnCombine: "function(partialA, partialB) { return (partialA + partialB) }"
            fnReset: "function() { return 0; }"
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

      - name: city_geoname_id
        type: NUMBER

      - name: continent_geoname_id
        type: NUMBER

      - name: country_geoname_id
        type: NUMBER

      - name: subdiv_1_geoname_id
        type: NUMBER

      - name: isp_asn
        type: NUMBER

      - name: session_id
        type: NUMBER

      - name: session_duration_class_s
        type: NUMBER

      - name: session_start
        type: NUMBER

      - name: session_stop
        type: NUMBER

      - name: viewing_id
        type: NUMBER

      - name: viewing_duration_class_s
        type: NUMBER

      - name: viewing_start
        type: NUMBER

      - name: viewing_start_time
        type: NUMBER

      - name: viewing_stop
        type: NUMBER

      - name: viewing_stop_time
        type: NUMBER

      - name: secure_link_expires
        type: NUMBER

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # kind: string
      # ^ (optional) the kind of the dimension. Can be 'string', 'time', 'number', or 'boolean'. Defaults to 'string'
      #
      # formula: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here
      #   See also the expressions API reference: https://plywood.imply.io/expressions
      #
      # url: string
      # ^ (optional) a url (including protocol) associated with the dimension, with optional token '%s'
      #   that is replaced by the dimension value to generate links specific to each value.

      - name: service_id
        title: Service id

      - name: playlist_id
        title: Playlist id

      - name: fromhost
        title: From host

      - name: fromhost_ip
        title: From host ip

      - name: remote_addr
        title: Remote addr

      - name: server_addr
        title: Server addr

      - name: server_protocol
        title: Server protocol

      - name: host
        title: Host

      - name: http_referer
        title: Http referer

      - name: http_accept_language
        title: Http accept language

      - name: http_x_forwarded_for
        title: Http x forwarded for

      - name: http_via
        title: Http via

      - name: http_user_agent
        title: Http user agent

      - name: city_geoname_id
        title: City geoname id

      - name: continent_geoname_id
        title: Continent geoname id

      - name: country_geoname_id
        title: Country geoname id

      - name: metro_code
        title: Metro code

      - name: time_zone
        title: Time zone

      - name: postal_code
        title: Postal code

      - name: subdiv_1_geoname_id
        title: Subdiv 1 geoname id

      - name: isp_asn
        title: ISP ASN

      - name: os_name
        title: OS name

      - name: os_family
        title: OS family

      - name: ua_name
        title: User agent name

      - name: ua_family
        title: User agent family

      - name: device_type
        title: Device type

      - name: browser_type
        title: Browser type

      - name: session_id
        title: Session id

      - name: session_duration_class_s
        title: Session duration class (s)

      - name: session_start
        title: Session start

      - name: session_stop
        title: Session stop

      - name: viewing_id
        title: Viewing id

      - name: viewing_duration_class_s
        title: Viewing duration class (s)

      - name: viewing_start
        title: Viewing start

      - name: viewing_start_time
        title: Viewing start time

      - name: viewing_stop
        title: Viewing stop

      - name: viewing_stop_time
        title: Viewing stop time

      - name: secure_link_expires
        title: Secure link expires (s)

      - name: language
        title: Language

      - name: quality
        title: Quality

      - name: media_id
        title: Media id

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: total_viewing_request_count
        title: Total viewing request count
        formula: $main.sum($viewing_total_request_count)

      - name: total_session_request_count
        title: Total session request count
        formula: $main.sum($session_total_request_count)

      - name: total_viewing_bytes_sent
        title: Total viewing bytes sent (byte)
        formula: $main.sum($viewing_total_bytes_sent)

      - name: total_session_bytes_sent
        title: Total session bytes sent (byte)
        formula: $main.sum($session_total_bytes_sent)

      - name: viewing_duration_s
        title: Viewing duration (s)
        formula: $main.sum($viewing_duration_s)

      - name: session_duration_s
        title: Session duration (s)
        formula: $main.sum($session_duration_s)

      - name: nb_viewings
        title: Number of viewings
        formula: $main.custom(unique_viewings_custom)

      - name: nb_sessions
        title: Number of sessions
        formula: $main.custom(unique_sessions_custom)

      - name: nb_medias
        title: Number of medias
        formula: $main.custom(unique_medias_custom)

      - name: avg_expire_delay_ms
        title: Average expire delay (ms)
        formula: $main.custom(expire_delay_ms) / $main.sum($count)

      - name: unique_viewings
        formula: $main.countDistinct($viewing_id)

      - name: unique_sessions
        formula: $main.countDistinct($session_id)

      - name: unique_ips
        formula: $main.countDistinct($remote_addr)

      - name: unique_medias
        formula: $main.countDistinct($media_id)

      - name: unique_users
        title: Unique users (IP)
        formula: $main.custom(unique_users)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)


  - name: all-filtered-rtmp-log-entries
    title: All filtered rtmp log entries
    clusterName: druid
    source: all-filtered-rtmp-log-entries

    # The refresh rule describes how often the data source looks for new data. Default: 'query'/PT1M (every minute)
    refreshRule:
      rule: realtime
      refresh: PT1M

    # The default timezone, expressed as an
    # <a href="https://en.wikipedia.org/wiki/Tz_database" target="_blank">
    # Olsen Timezone</a>, that will be selected when the user first opens this
    # cube. Default: <code>Etc/UTC</code>.
    defaultTimezone: Europe/Paris

    # The time period, expressed as an
    # <a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank">
    # ISO 8601 Duration</a>, that will be shown when the user first opens this
    # cube. Default: <code>P1D</code>.
    defaultDuration: P1D # <- default

    # The name of the measure that will be used for default sorting.
    # It is commonly set to the measure that represents the count of events.
    # Default: the first measure.
    defaultSortMeasure: count

    # The names of measures that are selected by default
    #defaultSelectedMeasures: []

    # The names of dimensions that are pinned by default (in order that they will appear in the pin bar)
    #defaultPinnedDimensions: []

    # How the dataset should be introspected
    # possible options are:
    # * none - Do not do any introspection, take what is written in the config as the rule of law.
    # * no-autofill - Introspect the datasource but do not automatically generate dimensions or measures
    # * autofill-dimensions-only - Introspect the datasource, automatically generate dimensions only
    # * autofill-measures-only - Introspect the datasource, automatically generate measures only
    # * autofill-all - (default) Introspect the datasource, automatically generate dimensions and measures
    introspection: none

    subsetFilter: $remote_addr.match('^195.178.113.').not()

    options:
      customAggregations:
        unique_users:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr"]
            byRow: true
        unique_viewers:
          aggregation:
            type: cardinality
            fieldNames: ["remote_addr","flash_version"]
            byRow: true
        count:
          aggregation:
            type: longSum
            fieldName: count

    # The list of attribute overrides in case introspection get something wrong
    attributeOverrides:
      # A general attribute override looks like so:
      #
      # name: user_unique
      # ^ the name of the attribute (the column in the database)
      #
      # type: STRING
      # ^ (optional) plywood type of the attribute
      #
      # special: unique
      # ^ (optional) any kind of special significance associated with this attribute

    # The list of dimensions defined in the UI. The order here will be reflected in the UI
    dimensions:
      # A general dimension looks like so:
      #
      # name: channel
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: The Channel
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # kind: string
      # ^ (optional) the kind of the dimension. Can be 'string', 'time', 'number', or 'boolean'. Defaults to 'string'
      #
      # formula: $channel
      # ^ (optional) the Plywood bucketing expression for this dimension. Defaults to '$name'
      #   if, say, channel was called 'cnl' in the data you would put '$cnl' here
      #   See also the expressions API reference: https://plywood.imply.io/expressions
      #
      # url: string
      # ^ (optional) a url (including protocol) associated with the dimension, with optional token '%s'
      #   that is replaced by the dimension value to generate links specific to each value.

      - name: service_id
        title: Service id

      - name: playlist_id
        title: Playlist id

      - name: fromhost
        title: From host

      - name: fromhost_ip
        title: From host ip

      - name: connection
        title: Connection

      - name: remote_addr
        title: Remote addr

      - name: app
        title: Application

      - name: name
        title: Name

      - name: args
        title: Args

      - name: flash_version
        title: Flash version

      - name: swf_url
        title: Swf URL

      - name: tc_url
        title: Tc URL

      - name: page_url
        title: Page URL

      - name: command
        title: Command

    # The list of measures defined in the UI. The order here will be reflected in the UI
    measures:
      # A general measure looks like so:
      #
      # name: avg_revenue
      # ^ the name of the dimension as used in the URL (you should try not to change these)
      #
      # title: Average Revenue
      # ^ (optional) the human readable title. If not set a title is generated from the 'name'
      #
      # formula: $main.sum($revenue) / $main.sum($volume) * 10
      # ^ (optional) the Plywood bucketing expression for this dimension.
      #   Usually defaults to '$main.sum($name)' but if the name contains 'min' or 'max' will use that as the aggregate instead of sum.
      #   this is the place to define your fancy formulas

      - name: count
        title: Count
        formula: $main.custom($count)

      - name: bytes_received
        title: Bytes Received (bytes)
        formula: $main.sum($bytes_received)

      - name: bytes_sent
        title: Bytes Sent (bytes)
        formula: $main.sum($bytes_sent)

      - name: session_time
        title: Session Time (s)
        formula: $main.sum($session_time)

      - name: avg_session_time
        title: Average Session Time (s)
        formula: $main.sum($session_time) / $main.sum($count)

      - name: unique_users
        title: Unique users (IP)
        formula: $main.custom(unique_users)

      - name: unique_viewers
        title: Unique viewers (IP + user agent)
        formula: $main.custom(unique_viewers)

      - name: unique_ips
        formula: $main.countDistinct($c_ip)
