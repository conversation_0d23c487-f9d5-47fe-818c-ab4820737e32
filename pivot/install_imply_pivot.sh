#!/bin/bash -e
display_usage(){
    echo -e "Usage: $0 <install|update|delete>"
}
if [ $# -ge 2 ]
then
    display_usage
    exit 1
fi
if [ $# -ge 1 ]
then
    OPTION=$1
else
    OPTION='install'
fi
BASE=$(dirname "$0")
SRC_DIR=$(cd "$BASE"; pwd)/src
case $OPTION in
install)
    mkdir -p "$SRC_DIR"
    git clone ssh://*****************************/diffusion/LOGIMPPI/imply-pivot.git "$SRC_DIR"
    ;;
update)
    cd "$SRC_DIR"
    git pull
    ;;
delete)
    echo "$SRC_DIR"
    rm -rI "$SRC_DIR"
    ;;
*)
    display_usage
    exit 1
    ;;
esac
