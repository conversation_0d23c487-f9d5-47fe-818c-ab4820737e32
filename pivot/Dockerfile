FROM node:6.5-slim

MAINTAINER <PERSON><PERSON><PERSON> <<EMAIL>>

# Setup environment variables.
ENV TZ=Europe/Paris \
    DEBIAN_FRONTEND=noninteractive

# Install supervisor.
RUN apt-get update && \
    apt-get -y dist-upgrade && \
    apt-get install -y --no-install-recommends supervisor && \
    echo $TZ > /etc/timezone && \
    dpkg-reconfigure -f noninteractive tzdata && \
    apt-get purge -y --auto-remove && \
    apt-get clean
     #&& \
    #rm -rf /var/cache/apt/archives/* /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy install scripts.
COPY install/install_pivot.sh /tmp

# Copy pivot sources.
COPY src /tmp/src

# Install pivot.
RUN cd /tmp/src && \
    /tmp/install_pivot.sh
     #&& \
    #rm -rf /tmp/*

# Add pivot user.
RUN groupadd -r pivot && \
    useradd -ms /bin/sh -r -g pivot pivot

WORKDIR /home/<USER>

VOLUME /etc/pivot \
       /etc/supervisor/conf.d \
       /var/log/pivot \
       /usr/local/bin/scripts

EXPOSE 9093-9102

CMD ["/usr/local/bin/scripts/entry.sh"]
