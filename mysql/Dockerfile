FROM mysql:5.7

MAINTAINER <PERSON><PERSON><PERSON> <<EMAIL>>

ENV TZ=Europe/Paris

# Configure timezone.
RUN echo $TZ > /etc/timezone && \
    dpkg-reconfigure -f noninteractive tzdata

# Install custom configuration files.
#COPY /etc /etc

RUN mkdir -p /var/log/mysql && \
    chown -R mysql:mysql /var/log/mysql

# RUN echo "CREATE DATABASE mydatabase CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" | mysql -u root

VOLUME /var/log/mysql \
       /var/lib/mysql \
