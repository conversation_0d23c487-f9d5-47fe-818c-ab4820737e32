#!/usr/bin/env bash
display_usage(){
    echo -e "Usage: $0"
}
if [ $# -ne 0 ]
then
    display_usage
    exit 1
fi
# Read root password
echo -n Current root user password:
read -sr root_password
echo
# Read new root password
echo -n New root user password:
read -sr new_root_password
echo
# Change root password
mysql -u root -p"$root_password" -e \
"SET PASSWORD FOR 'root'@'%' = PASSWORD('$new_root_password');"
