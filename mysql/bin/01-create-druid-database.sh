#!/usr/bin/env bash
display_usage(){
    echo -e "Usage: $0"
}
if [ $# -ne 0 ]
then
    display_usage
    exit 1
fi
# Read root password
echo -n Current root user password:
read -sr root_password
echo
# Read new druid password
echo -n druid user password:
read -sr druid_password
echo
mysql -u root -p"$root_password" -e \
"CREATE DATABASE IF NOT EXISTS druid DEFAULT CHARACTER SET utf8; \
GRANT ALL PRIVILEGES ON druid.* TO 'druid'@'%' IDENTIFIED BY '$druid_password'; \
FLUSH PRIVILEGES;"
