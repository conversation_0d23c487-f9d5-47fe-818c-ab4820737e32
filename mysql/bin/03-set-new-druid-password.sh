#!/usr/bin/env bash
display_usage(){
    echo -e "Usage:$0"
}
if [ $# -ne 0 ]
then
    display_usage
    exit 1
fi
# Read root password
echo -n Current root user password:
read -sr root_password
echo
# Read new druid password
echo -n New druid user password:
read -sr druid_password
echo
# Change druid password
mysql -u root -p"$root_password" -e \
"SET PASSWORD FOR 'druid'@'%' = PASSWORD('$druid_password');"
