#!/bin/bash -e
display_usage(){
    echo -e "Usage: $0 <install|update|delete>"
}
if [ $# -ge 2 ]
then
    display_usage
    exit 1
fi
if [ $# -ge 1 ]
then
    OPTION=$1
else
    OPTION='install'
fi
#./adminer/install_adminer.sh $OPTION
./filter/install_kafkalogfilter.sh $OPTION
./filter/install_hexaparser.sh $OPTION
./filter/install_uasparser3.sh $OPTION
./monitoring/install_logmonitor.sh $OPTION
./pivot/install_imply_pivot.sh $OPTION
./rsyslog/install_rsyslog-custom.sh $OPTION
./saver/install_loguploader.sh $OPTION
