#!/bin/bash -e
BASE=$(dirname "$0")
USER=$(whoami)
HOST_UID=$(id -u "$USER")
HOST_GID=$(id -g "$USER")
SYSLOG_UID=$(id -u syslog)
SYSLOG_GID=$(id -g syslog)
if [ ! -f "$BASE/docker-compose.env" ]
then
    printf 'HOST_UID=%s\nHOST_GID=%s\nSYSLOG_UID=%s\nSYSLOG_GID=%s' "$HOST_UID" "$HOST_GID" "$SYSLOG_UID" "$SYSLOG_GID" > "$BASE/docker-compose.env"
else
    sed -i "s/^\(HOST_UID\s*=\s*\).*$/\1$HOST_UID/" "$BASE/docker-compose.env" && \
    sed -i "s/^\(HOST_GID\s*=\s*\).*$/\1$HOST_GID/" "$BASE/docker-compose.env" && \
    sed -i "s/^\(SYSLOG_UID\s*=\s*\).*$/\1$SYSLOG_UID/" "$BASE/docker-compose.env" && \
    sed -i "s/^\(SYSLOG_GID\s*=\s*\).*$/\1$SYSLOG_GID/" "$BASE/docker-compose.env"
fi
if [ ! -f "$BASE/.env" ]
then
    cat > "$BASE/.env" <<-____HERE
CONFIG_NAME=default
BASE_NAME=logs
DOMAIN_NAME=logs-analytics.hexaglobe.com
MYSQL_DATA_DIR=./mysql/data
MYSQL_LOG_DIR=./mysql/log
RSYSLOG_LOG_DIR=./rsyslog/log
UPDATE_LOG_DIR=./rsyslog/data/update
SPOOL_LOG_DIR=./rsyslog/data/spool
ARCHIVE_LOG_DIR=./rsyslog/data/archive
ARCHIVE_DB_DIR=./mysql/archive
ZOOKEEPER_DATA_DIR=./zookeeper/data
ZOOKEEPER_TMP_DIR=/tmp/zookeeper
ZOOKEEPER_LOG_DIR=./zookeeper/log
KAFKA_DATA_DIR=./kafka/data
KAFKA_TMP_DIR=/tmp/data
KAFKA_LOG_DIR=./kafka/log
DRUID_TMP_DIR=/tmp/druid
DRUID_SNAPSHOT_DIR=/tmp/snapshot
INDEX_CACHE_DIR=./druid-historical/indexCache
BASE_PERSIST_DIR=./druid-realtime/basePersist
GEOIP_DOWNLOAD_DIR=./geoipupdate/geoipdownload
GEOIP_LOG_DIR=./geoipupdate/log
UA_DATABASE_DIR=./filter/database
SAVER_DRUID_LOG_DIR=./saver/log/druid
SAVER_RSYSLOG_LOG_DIR=./saver/log/rsyslog
MAILER_CONFIG_DIR=./mailer/config
MAILER_DATA_DIR=./mailer/data
MAILER_STATE_DIR=./mailer/state
PIVOT_LOG_DIR=./pivot/log
BROKER_LOG_DIR=./druid-broker/log
COORDINATOR_LOG_DIR=./druid-coordinator/log
HISTORICAL_LOG_DIR=./druid-historical/log
REALTIME_LOG_DIR=./druid-realtime/log
OVERLORD_LOG_DIR=./druid-overlord/log
APACHE2_LOG_DIR=./apache2/log
ADMINER_LOG_DIR=./adminer/log
____HERE
fi
