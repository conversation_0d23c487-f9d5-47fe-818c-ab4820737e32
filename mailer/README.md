# Logs Analytics docker mailer

Create the user before starting the container.
```shell
./mailer/add_mailer_user.sh
```

Build then start the container:
```shell
docker-compose -f ... build mailer
docker-compose -f ... up -d mailer
```

NB: may need to edit /etc/postfix/main.cf to change network ip to docker-compose network if different (done in entry.sh at start up).
```shell
docker exec logs_mailer grep "mynetworks\s*=" /etc/postfix/main.cf
docker exec logs_mailer sed -i 's/**********\32/**********\24/g' /etc/postfix/main.cf
```
