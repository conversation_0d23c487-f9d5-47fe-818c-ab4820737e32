#!/bin/bash -e
IP=$(ifconfig | sed -En 's/127.0.0.1//;s/.*inet (addr:)?(([0-9]*\.){3}[0-9]*).*/\2/p')
PREFIX=26
IFS=. read -r i1 i2 i3 i4 <<< "$IP"
IFS=. read -r xx m1 m2 m3 m4 <<< $(for a in $(seq 1 32); do if [ $(((a - 1) % 8)) -eq 0 ]; then echo -n .; fi; if [ "$a" -le $PREFIX ]; then echo -n 1; else echo -n 0; fi; done)
SUB=$(printf "%d.%d.%d.%d" "$((i1 & (2#$m1)))" "$((i2 & (2#$m2)))" "$((i3 & (2#$m3)))" "$((i4 & (2#$m4)))")
sed -i "s/^\(mynetworks\s*=\s*\).*$/\1*********\/8 \[::ffff:*********\]\/104 \[::1\]\/128 $SUB\/$PREFIX/" /etc/postfix/main.cf
exec /usr/local/bin/start-mailserver.sh
