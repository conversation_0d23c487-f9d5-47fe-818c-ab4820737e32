#!/bin/bash -e
display_usage(){
    echo -e "Usage:$0 mail_user"
}
if [ $# -le 0 ]
then
    display_usage
    exit 1
fi
BASE=$(dirname "$0")
BASE_DIR=$(cd "$BASE"; pwd)/..
# shellcheck source=./.env
source "$BASE_DIR/.env"
if [ -z "${MAILER_CONFIG_DIR}" ]; then
    echo "Need to set MAILER_CONFIG_DIR in .env file."
    exit 1
fi
if [[ ! "$MAILER_CONFIG_DIR" = /* ]]; then
    cd "${BASE_DIR}"
fi
user="$1"
read -srp "Enter Password: " password
echo
mkdir -p "${MAILER_CONFIG_DIR}"
touch "${MAILER_CONFIG_DIR}/postfix-accounts.cf"
docker run --rm \
  -e MAIL_USER="${user}" \
  -e MAIL_PASS="${password}" \
  -ti logs/mailer \
  /bin/sh -c 'echo "$MAIL_USER|$(doveadm pw -s SHA512-CRYPT -u $MAIL_USER -p $MAIL_PASS)"' >> "${MAILER_CONFIG_DIR}/postfix-accounts.cf"
