# MIGRATION DE DONNÉES DRUID D’UN DISQUE VERS UN AUTRE


## SOMMAIRE :
	
1- Copie des données local

2- Modification des config druid

3- Export anciens segments druid avec nouveau path

4- Import nouvelles data druid dans Derby

5- Restart de Druid


## 1- Copie des Données locales de Disque A vers Disque B

Le première étape d’une migration de données druid est donc la copie sur les disques des données du premier disque vers le nouveau. (Copie des dossiers segments uniquement ( unnecessary : indexing-logs, segment-cache, task)) en suivant le path indiqué dans les fichier de confs soit :
/DISK/druid/var/druid/

## 2- Modification des configs de Druid

Dans le dossier root de Druid vous pouvez accéder au dossier config puis aller dans les config de la version de druid que vous lancer (ex. single server -> micro quickstart).
Ensuite, vous pouvez notamment dans le historical/runtime.properties et dans les runtime.properties des autres services remplacer tous les /OLD_DISK/ par les /NEW_DISK/.
La configuration de druid est désormais à jour.

## 3- Export les anciennes datas de segments de druid (Derby) et les mettre à jour)

Afin d’effectuer cela vous devez effectuer ces deux commandes depuis le dossier root d'orgine de druid :

```mkdir -p /tmp/csv (création d’un dossier tmp qui stockera les dossier d’import export .csv)```

```java -classpath "lib/*" -Dlog4j.configurationFile=conf/druid/cluster/_common/log4j2.xml -Ddruid.extensions.directory="extensions" -Ddruid.extensions.loadList=[] org.apache.druid.cli.Main tools export-metadata --connectURI "*************************************************;" -n "{NEW_PATH}" --use-hex-blobs --booleans-as-strings -o /tmp/csv```


Dès lors, des fichiers csv ont été créer dans /tmp/csv comportant les infos des bases de données druid de Derby avec modification de tous les paths de segments par le nouveau indiqué avec l’option -n, de plus les options --use-hex-blobs et --booleans-as-strings permettent de convertir les csv en donnant importable dans Derby.

## 4- Import des nouvelles données dans Derby

Pour ce faire vous devez vous connecter à Derby avec la commande “ij” dans le serveur de destination après y avoir copier les /tmp/csv

```ij```


ensuite une fois dans derby vous devez vous connecter à votre base de données druid pour cela effectuer la commande suivante :

```connect '************************************************************';```

Vous êtes désormais connecté à votre DB Druid. Pour la mettre à jour avec les nouveaux CSV effectuez :

Un vide des anciennes table à remplacer afin d’éviter l’erreur de duplicata : 

```delete from DRUID_SEGMENTS;```
etc pour les 5 tables.

ensuite importer les données avec :

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'DRUID_SEGMENTS','/tmp/csv/druid_segments.csv',',','"',null,0);```

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'DRUID_RULES','/tmp/csv/druid_rules.csv',',','"',null,0);```

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'DRUID_CONFIG','/tmp/csv/druid_config.csv',',','"',null,0);```

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'DRUID_DATASOURCE','/tmp/csv/druid_dataSource.csv',',','"',null,0);```

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'DRUID_SUPERVISORS','/tmp/csv/druid_supervisors.csv',',','"',null,0);```


(cf. https://druid.apache.org/docs/latest/operations/export-metadata.html#importing-metadata)

Vous pourriez rencontré un problème avec le dernier import, un problème pour la key ID qui est en auto-increment pour régler cela, il faut supprimer la table et la recréer en remplaçant le auto-increment par GENERATED BY DEFAULT :

```drop table DRUID_SUPERVISORS;```

```CREATE TABLE DRUID_SUPERVISORS (ID BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY, SPEC_ID VARCHAR(255) NOT NULL, CREATED_DATE VARCHAR(255) NOT NULL , PAYLOAD BLOB NOT NULL);```

puis :

```CALL SYSCS_UTIL.SYSCS_IMPORT_TABLE (null,'SUPERVISORS_TEST','/tmp/csv/druid_supervisors.csv',',','"',null,0);```

vous pouvez désormais quitter Derby avec ```exit;```


## 5- Restart Druid

Vous pouvez désormais redémarrer druid et vos segments sont à jour et vos données migrer dans un nouveau disque.
