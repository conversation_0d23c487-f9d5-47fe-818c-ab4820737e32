#!/bin/bash -e
BASE=$(dirname "$0")
# shellcheck source=./.env
source "$BASE/.env"
if [ -z "${CONFIG_NAME}" ]; then
    echo "Need to set CONFIG_NAME in $BASE/.env file."
    exit 1
fi
if [ -z "${BASE_NAME}" ]; then
    echo "Need to set BASE_NAME in $BASE/.env file."
    exit 1
fi
function get_service_sub_network {
    if [ -z "$1" ]; then
        exit 1
    fi
    SERVICE="$1"
    NETWORK_MODE=$(docker inspect -f '{{.HostConfig.NetworkMode}}' "${BASE_NAME}_${SERVICE}")
    GATEWAY=$(docker inspect -f "{{.NetworkSettings.Networks.$NETWORK_MODE.Gateway}}" "${BASE_NAME}_${SERVICE}")
    PREFIX=$(docker inspect -f "{{.NetworkSettings.Networks.$NETWORK_MODE.IPPrefixLen}}" "${BASE_NAME}_${SERVICE}")
    printf "%s/%d" "$GATEWAY" "$PREFIX"
}
function set_rule_if_not_exists {
    if [ -z "$1" ]; then
        exit 1
    fi
    PID=$1
    if [ -z "$2" ]; then
        exit 1
    fi
    RULE=$2
    sudo nsenter -t "$PID" -n iptables -C $RULE 2>/dev/null || NOT_EXIST=1
    if [ ! -z "$NOT_EXIST" ]; then
        echo "-> iptables -A $RULE"
        sudo nsenter -t "$PID" -n iptables -A $RULE
    fi
}
function get_pid {
    if [ -z "$1" ]; then
        exit 1
    fi
    SERVICE="$1"
    docker inspect -f '{{.State.Pid}}' "${BASE_NAME}_${SERVICE}"
}
case $CONFIG_NAME in
prod-ingest)
    declare -A SERVICES=(['zookeeper']='2181' ['kafka']='9092' ['realtime']='8084' ['historical']='8083');
    for SERVICE in ${!SERVICES[*]}
    do
        PID=$(get_pid "${SERVICE}")
        SUB=$(get_service_sub_network "${SERVICE}")
        PORT=${SERVICES[${SERVICE}]}
        printf '%s (pid: %d, network: %s)\n' "$SERVICE" "$PID" "$SUB"
        set_rule_if_not_exists "$PID" "INPUT -p tcp -s $SUB,***************/32 --dport $PORT -j ACCEPT"
        set_rule_if_not_exists "$PID" "INPUT -p tcp --dport $PORT -j REJECT"
    done
    ;;
prod-front)
    declare -A SERVICES=(['mysql']='3306' ['coordinator']='8081' ['broker']='8082' ['historical']='8083');
    for SERVICE in ${!SERVICES[*]}
    do
        PID=$(get_pid "${SERVICE}")
        SUB=$(get_service_sub_network "${SERVICE}")
        PORT=${SERVICES[${SERVICE}]}
        printf '%s (pid: %d, network: %s)\n' "$SERVICE" "$PID" "$SUB"
        set_rule_if_not_exists "$PID" "INPUT -p tcp -s $SUB,195.178.113.37/32,***************/32 --dport $PORT -j ACCEPT"
        set_rule_if_not_exists "$PID" "INPUT -p tcp --dport $PORT -j REJECT"
    done
    # Adminer special case
    PID=$(get_pid adminer)
    SUB=$(get_service_sub_network adminer)
    printf 'adminer (pid: %d, network: %s)\n' "$PID" "$SUB"
    set_rule_if_not_exists "$PID" "INPUT -p tcp -s 193.248.54.17/32 --dport 80 -j ACCEPT"
    set_rule_if_not_exists "$PID" "INPUT -p tcp --dport 80 -j REJECT"
    ;;
*)
    echo "No need to set firewall for this configuration ($CONFIG_NAME)."
    ;;
esac
