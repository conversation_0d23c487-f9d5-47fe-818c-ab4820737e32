import os
import re
import numpy as np
import shutil
import glob
from datetime import datetime, timedelta
import dateutil.parser

date_min = datetime.today() - timedelta(days=5)
date_min = date_min.replace(hour=23).replace(minute=59)

for root, subdirs, files in os.walk("/data1/status"):
    if re.match("/path/to/status/[0-9]{4}/(0[1-9]|1[0-2])/(0[1-9]|[1-2][0-9]|3[0-1])/(2[0-3]|[0-1][0-9])", root):
        with os.scandir(root) as entries:
            minutes = []
            for entry in entries:
                if entry.name[-6:] == "status":
                    minutes.append(entry.name[:2])
            val = np.array(minutes)
            minutes = set(minutes)
            for elem in minutes:
                if np.count_nonzero(val == elem) == 16:
                    filepath = root + '/' + elem + '.XX.log.status'
                    done = []
                    for x in range(0, 16):
                        file = root + '/' + elem + '.' + str(x) + '.log.status'
                        with open(file, "r+") as f:
                            value = f.read()
                        done.append(value)
                    done = np.array(done)
                    data_path = root[18:]
                    data_path = '/path/to/data/main' + data_path + '/' + elem
                    test = data_path.split('/')
                    date_data = test[4] + '-' + test[5] + '-' + test[6]
                    date_data = dateutil.parser.parse(date_data)
                    if (np.count_nonzero(done == 'done') == 16 or np.count_nonzero(done == '') == 16) and date_data < date_min:
                        print(f"{datetime.today()} -- Deleted :" + data_path)
                        #Following command delete the minutes folder and the ignore error will void the directory does not exist error
                        try:
                            shutil.rmtree(data_path, ignore_errors=False)
                        except Exception as e:
                            print(f"{datetime.today()} -- Failed to delete {data_path}: {e}")
                        #delete the data_path
                        fileList = glob.glob(root + '/' + elem + '.*')
                        for filePath in fileList:
                            try:
                                os.remove(filePath)
                            except Exception as e:
                                print(f"{datetime.today()} -- Failed to delete {filePath}: {e}")
                        f = open(root + '/' + elem + '.final', "w")
                        f.write("Minute consume")

for root, subdirs, files in os.walk("/path/client-analytics-client-server/logs/parsed"):
    if re.match("/path/client-analytics-client-server/logs/parsed/[0-9]{4}/([1-9]|1[0-2])/", root):
        date = dateutil.parser.parse(root.split('/')[5] + '-' + root.split('/')[6] + '-' + root.split('/')[7])
        date_min_ws = datetime.today() - timedelta(days=31)
        date_min_ws = date_min_ws.replace(hour=23).replace(minute=59)
        if date < date_min_ws:
            print(f"{datetime.today()} -- Deleting WEBSOCKET SIDE : {root}")
            shutil.rmtree(root, ignore_errors=False)
