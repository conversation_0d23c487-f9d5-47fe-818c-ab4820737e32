version: '2'

services:
  adminer:
    build: ./adminer
    image: ${BASE_NAME}/adminer
    container_name: ${BASE_NAME}_adminer
    volumes:
      - ${ADMINER_LOG_DIR}:/var/log/apache2
      - ./adminer/.htpasswd:/etc/htpasswd/.htpasswd:ro
      - ./adminer/etc/apache2/httpd.conf:/etc/apache2/httpd.conf:ro
      - ./adminer/vhosts:/etc/apache2/vhosts:ro
    links:
      - php:php-provider
      - coordinator:druid-coordinator
      - broker:druid-broker
      - historical:druid-historical-front
      - overlord:druid-overlord
    extra_hosts:
      - "druid-realtime:**************"
      - "druid-historical-ingest:**************"
    ports:
      - 8180:80
    restart: unless-stopped
  mailer:
    build: ./mailer
    hostname: mail
    domainname: ${DOMAIN_NAME}
    image: ${BASE_NAME}/mailer
    container_name: ${BASE_NAME}_mailer
    volumes:
      - ${MAILER_DATA_DIR}:/var/mail
      - ${MAILER_STATE_DIR}:/var/mail-state
      - ${MAILER_CONFIG_DIR}:/tmp/docker-mailserver
      - ./mailer/bin/entry.sh:/usr/local/bin/entry.sh:ro
    restart: unless-stopped
  mysql:
    build: ./mysql
    image: ${BASE_NAME}/mysql
    container_name: ${BASE_NAME}_mysql
    volumes:
      - ./mysql/entrypoint:/docker-entrypoint-initdb.d:ro
      - ${MYSQL_DATA_DIR}:/var/lib/mysql
      - ${MYSQL_LOG_DIR}:/var/log/mysql
      - ./mysql/bin:/usr/local/bin/mysql:ro
    environment:
      - MYSQL_RANDOM_ROOT_PASSWORD=true
    ports:
      - 3306:3306
    restart: unless-stopped
  php:
    build: ./php
    image: ${BASE_NAME}/php
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_php
    volumes:
      - ./adminer/src:/var/www/adminer:ro
      - ./php/log:/var/log/php7
      - ./php/bin:/usr/local/bin:ro
    links:
      - mysql:db-provider
    restart: unless-stopped
  coordinator:
    build: ./druid-coordinator
    image: ${BASE_NAME}/coordinator
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_coordinator
    hostname: druid-coordinator
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-coordinator/etc/${CONFIG_NAME}:/etc/druid/coordinator:ro
      - ${COORDINATOR_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/coordinator:/data/druid/tmp
      - ${DRUID_SNAPSHOT_DIR}/coordinator:/data/druid/snapshot
    links:
      - mysql:db-provider
    ports:
      - 8081:8081
    extra_hosts:
      - "zookeeper-server:**************"
    restart: unless-stopped
  historical:
    build: ./druid-historical
    image: ${BASE_NAME}/historical
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_historical
    hostname: druid-historical
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-historical/etc/${CONFIG_NAME}:/etc/druid/historical:ro
      - ${HISTORICAL_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/historical:/data/druid/tmp
      - ${INDEX_CACHE_DIR}:/data/druid/indexCache
    links:
      - mysql:db-provider
    ports:
      - 8083:8083
    extra_hosts:
      - "zookeeper-server:**************"
    restart: unless-stopped
  broker:
    build: ./druid-broker
    image: ${BASE_NAME}/broker
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_broker
    hostname: druid-broker
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-broker/etc/${CONFIG_NAME}:/etc/druid/broker:ro
      - ${BROKER_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/broker:/data/druid/tmp
    links:
      - mysql:db-provider
      - historical:druid-historical-front
    ports:
      - 8082:8082
    extra_hosts:
      - "zookeeper-server:**************"
      - "druid-realtime:**************"
      - "druid-historical-ingest:**************"
    restart: unless-stopped
  overlord:
    build: ./druid-overlord
    image: ${BASE_NAME}/overlord
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_overlord
    hostname: druid-overlord
    volumes:
      - ./druid-base/etc/${CONFIG_NAME}:/etc/druid/_common:ro
      - ./druid-overlord/etc/${CONFIG_NAME}:/etc/druid/overlord:ro
      - ${OVERLORD_LOG_DIR}:/var/log/druid
      - ./druid-base/bin:/usr/local/bin:ro
      - ${DRUID_TMP_DIR}/overlord:/data/druid/tmp
    links:
      - mysql:db-provider
    extra_hosts:
      - "zookeeper-server:**************"
    restart: unless-stopped
  pivot:
    build: ./pivot
    image: ${BASE_NAME}/pivot
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_pivot
    links:
      - broker:druid-broker
    volumes:
      - ./pivot/etc/${CONFIG_NAME}/pivot:/etc/pivot:ro
      - ./pivot/etc/${CONFIG_NAME}/supervisor/conf.d:/etc/supervisor/conf.d:ro
      - ${PIVOT_LOG_DIR}:/var/log/pivot
      - ./pivot/bin:/usr/local/bin/scripts:ro
    restart: unless-stopped
  apache2:
    build: ./apache2
    image: ${BASE_NAME}/apache2
    container_name: ${BASE_NAME}_apache2
    links:
      - pivot:pivot-provider
    volumes:
      - ${APACHE2_LOG_DIR}:/var/log/apache2
      - ./apache2/.htpasswd:/etc/htpasswd/.htpasswd:ro
      - ./apache2/etc/apache2/httpd.conf:/etc/apache2/httpd.conf:ro
      - ./apache2/vhosts:/etc/apache2/vhosts:ro
    ports:
      - 80:80
    restart: unless-stopped
  monitoring:
    build: ./monitoring
    image: ${BASE_NAME}/monitoring
    env_file: docker-compose.env
    container_name: ${BASE_NAME}_monitoring
    volumes:
      - ./monitoring/bin/entry.sh:/usr/local/bin/entry.sh:ro
      - ./monitoring/log:/var/log
      - ./monitoring/etc/${CONFIG_NAME}:/etc/druid:ro
      - ./monitoring/src:/home/<USER>/logmonitor:ro
    links:
      - broker:druid-broker
      - mailer:mailer-provider
    restart: unless-stopped