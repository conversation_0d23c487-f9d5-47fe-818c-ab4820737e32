FROM alpine:3.4

MAINTAINER <PERSON>-<PERSON> <<EMAIL>>

COPY install /tmp

RUN apk update --no-cache && \
    apk upgrade --no-cache && \
    apk add --update --no-cache mysql-client tzdata && \
    cp /usr/share/zoneinfo/Europe/Paris /etc/localtime && \
    echo "Europe/Paris" > /etc/timezone && \
    /tmp/install_s3cmd.sh && \
    apk del tzdata && \
    rm -rf /var/cache/apk/* /tmp/*

ENV SAVER_HOME_DIR=/home/<USER>/loguploaderpackage \
    SAVER_LOG_DIR=/var/log \
    SAVER_CFG_DIR=/etc/saver

WORKDIR /home/<USER>

VOLUME /var/log/rsyslog \
       /var/log/druid \
       /data/log/update \
       /data/log/archive \
       /data/druid/sql \
       /home/<USER>/loguploaderpackage \
       /home/<USER>/.s3cfg \
       /etc/saver \
       /usr/local/bin

CMD ["/usr/local/bin/entry.sh"]
