[COMMON_CONFIG]
debug=False
log-upload-config=LOG_UPLOADER_CONFIG

[LOG_UPLOADER_CONFIG]
s3-config=S3_CONFIG
database-config=DATABASE_CONFIG
database-upload-config=S3_DATABASE_UPLOAD_CONFIG
log-config=LOG_CONFIG
log-upload-config=S3_LOG_UPLOAD_CONFIG
mailer-config=LOCAL_MAILER_CONFIG

[S3_CONFIG]
config-file=/home/<USER>/.s3cfg

[DATABASE_CONFIG]
host=db-provider
user=druid
database=druid
password=

[LOCAL_DATABASE_UPLOAD_CONFIG]
type=local
dest-dir=/data/druid/sql
max-days=30

[S3_DATABASE_UPLOAD_CONFIG]
type=s3
local-dir=/data/druid/sql
local-max-days=30
s3-bucket=logs-druid.hexaglobe.com
s3-base-key=PROD/sql
s3-max-days=30

[LOG_CONFIG]
log-dir=/data/log/update
log-min-hours=0

[LOCAL_LOG_UPLOAD_CONFIG]
type=local
dest-dir=/data/log/archive
local-max-days=7

[S3_LOG_UPLOAD_CONFIG]
type=s3
local-dir=/data/log/archive
local-max-days=7
s3-bucket=logs-raw.hexaglobe.com
s3-base-key=PROD
s3-min-hours=0
s3-max-weeks=

[LOCAL_MAILER_CONFIG]
host=mailer-provider
from-display-name=Log uploader mailer
from-username=log-uploader
to-display-name=Log uploader developer
to-username=<EMAIL>
