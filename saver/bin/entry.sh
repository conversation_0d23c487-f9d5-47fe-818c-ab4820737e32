#!/bin/sh -e
getent group druid &>/dev/null || ( \
    addgroup druid -g "$HOST_GID" && \
    adduser -G druid -s /bin/sh -D druid -u "$HOST_UID" \
)
getent group syslog &>/dev/null || ( \
    addgroup syslog -g "$SYSLOG_GID" && \
    adduser -G syslog -s /bin/sh -D syslog -u "$SYSLOG_UID" \
)
chown syslog:syslog /data/log/update /data/log/archive /var/log/rsyslog
chown druid:druid /data/druid/sql /var/log/druid
chmod u+s /bin/busybox
crontab -u root /etc/saver/syslog/crontab
crontab -u druid /etc/saver/druid/crontab
exec crond -f -l 7 -L /dev/stdout
