curl http://data.udger.com/XXXXXXXXXXXXXXXXXXXXXXXXXXXXX/udgerdb_v3.dat > /filter_location/udgerdb_v3.dat && chown acourcoux:acourcoux /filter_location/udgerdb_v3.dat
size=$( wc -c /filter_location/udgerdb_v3.dat | awk '{print $1}' )
if [ "$size" -gt 1000000 ]
then
  cp /filter_location/udgerdb_v3.dat /filter_location/day"$(date +%u)"/udgerdb_v3.dat && chown acourcoux:acourcoux /filter_location/day"$(date +%u)"/udgerdb_v3.dat
fi


curl http://data.udger.com/XXXXXXXXXXXXXXXXXXXXXXXXXXXX/udgerdb_v3.dat > /ws_location/udgerdb_v3.dat
size=$( wc -c /ws_location/udgerdb_v3.dat | awk '{print $1}' )
if [ "$size" -gt 1000000 ]
then
  cp /ws_location/udgerdb_v3.dat /ws_location/day"$(date +%u)"/udgerdb_v3.dat
fi

# PROD UPDATE GEOIP

#geoipupdate -d path_to_logsanalytics/geoipupdate/geoipdownload -f ./GeoIP.conf && chown user:user *.mmdb
#geoipupdate -d path_to_client-advanced-analytics-server/wsserver/ws_parser/src/databases -f ./GeoIP.conf
