<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  meteoconsult-logs-pivot.hexaglobe.net
    ServerAlias meteoconsult-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/meteoconsult_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/meteoconsult_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9093/
    ProxyPassReverse  / http://pivot-provider:9093/
    <Location />
        AuthName     "Meteoconsult Logs Analytics Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/meteoconsult
        require      valid-user
    </Location>
</VirtualHost>
