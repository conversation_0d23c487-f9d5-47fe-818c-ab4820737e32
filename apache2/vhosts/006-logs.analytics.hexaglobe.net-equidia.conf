<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  equidia-logs-pivot.hexaglobe.net
    ServerAlias equidia-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/equidia_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/equidia_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9096/
    ProxyPassReverse  / http://pivot-provider:9096/
    <Location />
        AuthName     "Equidia Logs Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/equidia
        require      valid-user
    </Location>
</VirtualHost>
