<VirtualHost *:80>
    ServerAd<PERSON> <EMAIL>
    ServerName  zeturf-logs-pivot.hexaglobe.net
    ServerAlias zeturf-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/zeturf_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/zeturf_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass        / http://pivot-provider:9097/
    ProxyPassReverse / http://pivot-provider:9097/
    <Location />
        AuthName     "Zeturf Logs Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/zeturf
        require      valid-user
    </Location>
</VirtualHost>
