<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  hexaglobe-logs-pivot.hexaglobe.net
    ServerAlias hexaglobe-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/hexaglobe_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/hexaglobe_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9090/
    ProxyPassReverse  / http://pivot-provider:9090/
    <Location />
        AuthName     "Hexaglobe Logs Analytics Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/hexaglobe
        require      valid-user
    </Location>
</VirtualHost>
