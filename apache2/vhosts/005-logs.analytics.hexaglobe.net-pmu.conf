<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  pmu-logs-pivot.hexaglobe.net
    ServerAlias pmu-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/pmu_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/pmu_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9095/
    ProxyPassReverse  / http://pivot-provider:9095/
    <Location />
        AuthName     "Pmu Logs Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/pmu
        require      valid-user
    </Location>
</VirtualHost>
