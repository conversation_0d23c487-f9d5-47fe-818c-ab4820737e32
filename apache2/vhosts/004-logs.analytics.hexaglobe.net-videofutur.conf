<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  videofutur-logs-pivot.hexaglobe.net
    ServerAlias videofutur-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/videofutur_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/videofutur_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9094/
    ProxyPassReverse  / http://pivot-provider:9094/
    <Location />
        AuthName     "Videofutur Logs Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/videofutur
        require      valid-user
    </Location>
</VirtualHost>
