<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  euronews-logs-pivot.hexaglobe.net
    ServerAlias euronews-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/euronews_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/euronews_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass        / http://pivot-provider:9098/
    ProxyPassReverse / http://pivot-provider:9098/
    <Location />
        AuthName     "Euronews Logs Access"
        AuthType     Basic
        AuthUserFile ${APACHE_PASSWD_DIR}/euronews
        require      valid-user
    </Location>
</VirtualHost>
