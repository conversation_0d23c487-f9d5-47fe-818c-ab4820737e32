<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName  universcine-logs-pivot.hexaglobe.net
    ServerAlias universcine-logs-pivot.hexaglobe.net
    ErrorLog    ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/universcine_error.log
    LogFormat   "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %V\"" combined
    CustomLog   ${APACHE_LOG_DIR}/logs.analytics.hexaglobe.net/universcine_access.log combined
    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>
    ProxyPreserveHost On
    ProxyPass         / http://pivot-provider:9091/
    ProxyPassReverse  / http://pivot-provider:9091/
    <Location />
        AuthName     "Universcine Logs Access"
        AuthType     Basic
        AuthUserFile /etc/htpasswd/.htpasswd/universcine
        require      valid-user
    </Location>
</VirtualHost>
