FROM alpine:3.4

MAINTAINER <PERSON><PERSON> <<EMAIL>>

RUN apk --no-cache update && \
    apk --no-cache upgrade && \
    apk --update add --no-cache apache2-proxy tzdata && \
    cp /usr/share/zoneinfo/Europe/Paris /etc/localtime && \
    echo "Europe/Paris" > /etc/timezone && \
    apk del tzdata && \
    rm -rf /var/cache/apk/*

RUN mkdir -p /run/apache2 /var/www/logs && \
    mkdir /etc/apache2/vhosts && \
    rm /etc/apache2/httpd.conf

ENV APACHE_LOG_DIR=/var/log/apache2 \
    APACHE_PASSWD_DIR=/etc/htpasswd/.htpasswd

# Expose volumes.
VOLUME /var/log/apache2 \
       /etc/htpasswd/.htpasswd \
       /etc/apache2/httpd.conf \
       /etc/apache2/vhosts

# Expose ports.
EXPOSE 80 443

CMD ["httpd", "-e", "info", "-D", "FOREGROUND"]
