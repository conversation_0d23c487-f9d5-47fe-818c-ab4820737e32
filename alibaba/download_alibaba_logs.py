import base64
import gzip
import hashlib
import hmac
import json
import logging
import os
import re
import time
import uuid
from datetime import datetime, timedelta, timezone
from urllib.parse import quote

from pip._internal.utils.retry import retry

import requests
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
logging.basicConfig(filename='script.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

load_dotenv()
ACCESS_KEY_ID = os.getenv("ALIBABA_ACCESS_KEY_ID")
ACCESS_KEY_SECRET = os.getenv("ALIBABA_ACCESS_KEY_SECRET")


def sign(string_to_sign, secret):
    h = hmac.new(secret.encode(), string_to_sign.encode(), hashlib.sha1)
    return base64.b64encode(h.digest()).decode()


# Constructing the request parameters
def generate_parameters():
    now = datetime.now(timezone(timedelta(hours=8)))
    start = (now - timedelta(hours=25)).strftime("%Y-%m-%dT%H:%M:%SZ")
    end = (now - timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
    params = {
        "Action": "DescribeCdnDomainLogs",
        "Format": "JSON",
        "Version": "2018-05-10",
        "AccessKeyId": ACCESS_KEY_ID,
        "SignatureMethod": "HMAC-SHA1",
        "Timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
        "SignatureVersion": "1.0",
        "SignatureNonce": str(uuid.uuid4()),
        "DomainName": "media-001.bhama.net",
        "StartTime": start,
        "EndTime": end,
    }
    return params


def create_signature(params, access_key_secret):
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    canonicalized_query_string = "&".join(["{}={}".format(k, quote(v, safe="")) for k, v in sorted_params])
    string_to_sign = "GET&%2F&" + quote(canonicalized_query_string, safe="")
    signature = sign(string_to_sign, access_key_secret + "&")
    return signature


def get_log_info_details():
    """
    Get log info details from Alibaba Cloud
    """
    params = generate_parameters()
    params["Signature"] = create_signature(params, ACCESS_KEY_SECRET)
    url = "https://cdn.aliyuncs.com/?" + "&".join(["{}={}".format(k, quote(v, safe="")) for k, v in params.items()])

    response = requests.get(url)
    if response.status_code == 200:
        data = json.loads(response.text)
        log_info_details = [
            log_info_detail
            for domain in data["DomainLogDetails"]["DomainLogDetail"]
            for log_info_detail in domain["LogInfos"]["LogInfoDetail"]
        ]
        return log_info_details
    else:
        logger.error(f"Failed to get log info details, status code: {response.status_code}")
        return None


# Main function to download logs
@retry(stop_after_delay=4, wait=1)
def download_logs():
    target_base = "/data1/rsyslog-ingest/main"
    os.makedirs(target_base, exist_ok=True)
    pattern = re.compile(r"_([0-9]{4})_([0-9]{2})_([0-9]{2})_([0-9]{2})([0-9]{2})[0-9]{2}")
    log_info_details = get_log_info_details()
    for item in log_info_details:
        # get log file name and size
        log_name = item["LogName"][:-3]
        match = pattern.search(log_name)
        if match:
            year, month, day, hour, minute = match.groups()
            target_dir = os.path.join(target_base, year, month, day, hour, minute)
            os.makedirs(target_dir, exist_ok=True)
            target_path = os.path.join(target_dir, "alibaba--" + log_name)

            # Check if path already exists
            if os.path.exists(target_path) or os.path.exists(os.path.splitext(target_path)[0]):
                logger.info(f"File already exists: {log_name} in {target_dir}")
                continue

            log_url = f"https://{item['LogPath']}" if not item["LogPath"].startswith("http") else item["LogPath"]
            # Download the log file
            response = requests.get(log_url)
            if response.status_code == 200:
                try:
                    unzipped = gzip.decompress(response.content)
                    with open(target_path, "xb") as f:
                        f.write(unzipped)
                    logger.info(f"Downloaded: {log_name} in {target_dir}")
                except Exception as e:
                    logger.error(f"Error writing to {target_path}: {e}")
            else:
                logger.error(f"Failed to download: {log_name}, status code: {response.status_code}")


if __name__ == "__main__":
    try:
        download_logs()
    except Exception as e:
        logger.error(f"An error occurred: {e}")
