# Logsanalytics docker


## 1. External dependencies.

In order to run this project, you must first install both docker engine and docker-compose packages.

See following links for more details:

[How to install docker](https://docs.docker.com/engine/installation/)

[How to install docker-compose](https://docs.docker.com/compose/install/)


## 2. Create/update environment configuration.

Then, create/update the docker compose environment file (uid and gid) by running this line as the expected user:
```shell
./generate_env_file.sh
```
Edit the generated file .env accordingly to your configuration.
By default, all data is stored in project subfolders. Be careful about disk space before launch services (especially rsyslog).

You may need to manually create destination folders if you want these folders to have the correct permission rights. Otherwise they will be created as root owner by the docker daemon.

You should also check the docker-compose.yml relevant to your use in order to change the kafkahost accordingly to your needs .

Also modify kafkahost in config file (filter/etc/default/test_archive_file_config00.ini) with the ip of the machine hosting your kafka

## 3. Install project sources.

Install the necessary source dependencies (only the first time):
Just the kafkalogfilter and the Hexaparser dependency is needed.
```shell
./install_dependencies.sh install
```
Eventually update the source files if necessary:
```shell
./install_dependencies.sh update
```
Eventually clean the source files:
```shell
./install_dependencies.sh delete
```

Check the Hexaparser repository and README for more details on how to configure the hexaparser dependency.

## 4. Build images.
Build all docker images:
```shell
docker-compose build
```

## 5. Add externals dependencies
Download geoip mmdb files (ask Pierre-Alexandre ENTRAYGUES <<EMAIL>> for the files)
Add them in the 'geoipupdate/geoipdownload/' directory.

Download the udgerdb_v3.dat (ask Pierre-Alexandre ENTRAYGUES <<EMAIL>> for the files)
Add them it in the 'filter/database/filter' directory.

Then check that a cron task is set to maintain those databases up to date (geoipupdate / udger curl)

## 6. Configure UID/GID to run it clean
    On the server you are running create a user with a UID/GID that you will set in the docker-compose.yml file/docker-compose.env.
    Then, you will have to configure the chown rights for this specific anlytics user on the /status folder on your server.
    Then you will be able to run the docker-compose up command without any permission issue.

## 7. Start all services.
Start all services by launching:
```shell
docker-compose up -d
```

## EXTRA. How to consume cloudfront logs from s3 bucket (overflow)
    location : analytics prod - /data1/aws-s3-bucketloghorses/{service-name}/{compressed_log_files}

    1. Before copying the logs from s3 to the server, you need to stop filter.
        - To do so, you need to one by one stop them after they log : "NO NEW DIRECTORY FOUND" meaning they are in a pause state.
    2. Then you have to copy / decompress to the right directory all the files of a day for each service using the following command :
        ```for f in /data1/aws-s3-bucketloghorses-synced/live2/EBNACVN2R9VUC.YYYY-MM-DD* ; do PREF=$(basename -s .gz "${f}") && gunzip -c "$f" > /data1/rsyslog-ingest/main/YYYY/MM/DD/HH/mm/equidia-live2-live."${PREF}"; done``` (this example is for live2 service using its id in S3 : EBNACVN2R9VUC)
    3. After you get all the cloudfront files, check the rights ! It needs to be the same as the other regulars logs
    4. Then you can restart the filter services so they will consume the unconsumed new directories including the minutes where you copied the cloudfront logs.

## EXTRA. DVR HBBTV management with redis
    - Just know that the redis conf is directly in : src/kafkalogfilter/formatter/abstractlogmessage.py (host = redis in prod and localhost for local testing)