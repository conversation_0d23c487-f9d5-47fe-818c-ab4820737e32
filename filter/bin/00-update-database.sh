#!/bin/sh -e
BASE_DIR='/opt/filter'
if [ -z "${FILTER_CFG_DIR}" ]; then
    CFG_DIR="${BASE_DIR}"
else
    CFG_DIR="${FILTER_CFG_DIR}"
fi
if [ $# -eq 0 ]
then
    eval $(grep '^access_key=' "${CFG_DIR}/udger.conf")
else
    access_key="$1"
fi
cd "${BASE_DIR}"
if [ -z "${FILTER_ID}" ]; then
   CFG_NAME='update_config_1.ini'
else
   CFG_NAME="update_config_${FILTER_ID}.ini"
fi
RELOAD_PORT=$(sed -nr 's/^port:\s*([0-9]+)\s*$/\1/p' "${CFG_DIR}/${CFG_NAME}")
exec ./reload_user_agent.py "${CFG_DIR}/${CFG_NAME}" "http://data.udger.com/$access_key/udgerdata_old.ini" "http://data.udger.com/$access_key/udgerdata_old_ini.md5" localhost "${RELOAD_PORT}" --monitor --no-reload
