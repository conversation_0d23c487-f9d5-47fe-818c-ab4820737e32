#!/bin/sh -e
[ $# -eq 0 ] && { echo "Usage: $0 access_key"; exit 1; }
access_key="$1"
BASE_DIR='/opt/filter'
if [ -z "${FILTER_CFG_DIR}" ]; then
    CFG_DIR="${BASE_DIR}"
else
    CFG_DIR="${FILTER_CFG_DIR}"
fi
cd "${BASE_DIR}"
exec ./reload_user_agent.py "${CFG_DIR}/update_config.ini" "http://data.udger.com/$access_key/udgerdata_old.ini" "http://data.udger.com/$access_key/udgerdata_old_ini.md5" localhost 18812 --monitor
