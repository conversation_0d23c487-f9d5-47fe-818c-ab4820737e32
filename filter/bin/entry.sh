#!/bin/sh -e

getent group filter &>/dev/null || ( \
    groupadd filter -g "$HOST_GID" && \
    useradd -g filter -s /bin/sh -u "$HOST_UID" filter)
chown filter:filter /var/log /usr/local/share/udger/database
#mkdir /usr/local/share/udger/database/20200101
#mkdir /usr/local/share/udger/database/20200101/00
## Les cmd pour que le recov fonctionne
#apt install -y s3cmd
#apt install -y xz-utils
#cp /etc/filter/.s3cfg /root/.s3cfg
## Les cmd pour que le recov fonctionne


BASE=$(dirname "$0")
# Create/update udger database at startup (ignore errors).
#sudo -E -u filter "$BASE/00-update-database.sh" || true 2>&1
# Run the infinite consume task here.
exec sudo -E -u filter "$BASE/01-run-filter-update.sh" 2>&1
