#!/bin/sh -e
BASE_DIR='/opt/filter'
if [ -z "${FILTER_CFG_DIR}" ]; then
    CFG_DIR="${BASE_DIR}"
else
    CFG_DIR="${FILTER_CFG_DIR}"
fi
cd "${BASE_DIR}"
if [ -z "${FILTER_ID}" ]; then
   CFG_NAME='test_archive_file_config00.ini'
else
   CFG_NAME="test_archive_file_config00.ini"
fi
#exec ./run.py "${CFG_DIR}/${CFG_NAME}" 2>&1
exec python3 -u ./run.py "${CFG_DIR}/${CFG_NAME}" --modulo_result "$SCALE_NB" 2>&1


#BASE_DIR='/opt/filter'
#if [ -z "${FILTER_CFG_DIR}" ]; then
#    CFG_DIR="${BASE_DIR}"
#else
#    CFG_DIR="${FILTER_CFG_DIR}"
#fi
#cd "${BASE_DIR}"
#if [ -z "${FILTER_ID}" ]; then
#   CFG_NAME='update_config_1.ini'
#else
#   CFG_NAME="update_config_${FILTER_ID}.ini"
#fi
#exec ./run.py "${CFG_DIR}/${CFG_NAME}" --modulo_result "$SCALE_NB" $2>&1
