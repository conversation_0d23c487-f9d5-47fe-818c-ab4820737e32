[COMMON_CONFIG]
type: archive-log
debug: False
serialize-tables: False

[LOG_FILTER_CONFIG]
consumer: LOG_FILE_CONSUMER_CONFIG
formatter: FORMATTER_CONFIG
viewing: VIEWING_CONFIG
session: SESSION_CONFIG
producer: P<PERSON><PERSON>F<PERSON>_PRODUCER_CONFIG
user-agent: UAS_PARSER3_AGENTS_USER_AGENT_CONFIG
mailer-config = LOCAL_MAILER_CONFIG

[PYKAFKA_SIMPLE_CONSUMER_CONFIG]
type: pykafka
log-ingestion: True
kafka-type: simple-consumer
topic: all-service-entries
consumer-group: log.filter.consumer.test2
server: **********:9092
use-rdkafka: True
fetch-min-bytes: 1024
fetch-message-max-bytes: 2097152
auto-commit-enable: True
auto-commit-interval-ms: 15000
auto-offset-reset: earliest
num-consumer-fetchers: 4
queued-max-messages: 5000

[PYKAFKA_BALANCED_CONSUMER_CONFIG]
type: pykafka
num-consumers: 4
log-ingestion: True
kafka-type: balanced-consumer
topic: all-service-entries
consumer-group: log.filter.consumer.test3
server: **********:9092
use-rdkafka: True
fetch-min-bytes: 1024
fetch-message-max-bytes: 2097152
auto-commit-enable: True
auto-commit-interval-ms: 15000
auto-offset-reset: earliest
num-consumer-fetchers: 4
queued-max-messages: 5000
zookeeper-connect: **********:2181

[LOG_FILE_CONSUMER_CONFIG]
type: log-file
syslog-tag: nginx
#laisser nginx
fromhost: cachecdn9
#simuler rsyslog / premier #
fromhost-ip: **************
# recuperer l'ip dans le nom du fichier
#input-dir: /home/<USER>/Telechargements/
input-dir: /var/log/
;input-dir: /var/log/
;input-file: hexa_analytics_harald\.log(\.1)?$
;input-file: .*\.log
;input-file: .*\.tar\.xz
;input-file: .*\.dir
input-file: /var/log/[0-9]{4}/(0[1-9]|1[0-2])/(0[1-9]|[1-2][0-9]|3[0-1])/(2[0-3]|[0-1][0-9])/[0-5][0-9]
start-date: 2024-05-22:00:00:00
stop-date: 2099-12-31:23:00:00
druid-address: ************:8888
cookie_signer_passphrase: av7Pb2If56m4%%qv5$Or2XwtyLi2%%ryZxynS8&xu2m!^LEepz7j

[ARCHIVE_FILE_CONSUMER_CONFIG]
type: archive-file
syslog-tag: nginx
period: daily
#input-dir: /opt/filter/LOCALFILE/logs-rsyslog/
#input-file: *.log
;input-dir: /var/log/recov/094c586d-741e-47c4-a48f-230c0fecd06b/logs-rsyslog/
input-dir: /var/log/recov/0a412fed-cea6-4b12-be10-8f7133a32bb90a412fed-cea6-4b12-be10-8f7133a32bb9/logs-rsyslog/
start-date: 2020-08-11:00:00:00
stop-date: 2020-01-01:03:00:00
#period: daily
#input-dir: /logs/eqd-pmu-live/eqd-pmu-live/
#start-date: 2015-06-01:00:00:00
#stop-date: 2016-01-01:00:00:00

[FORMATTER_CONFIG]
log-active: False
log-unknown-service: False
log-unknown: False
log-invalid: False
unknown-service-destination: /var/log/recov/logs/unknown-service.log
unknown-destination: /var/log/recov/logs/unknown.log
invalid-destination: /var/log/recov/logs/invalid.log

;[FORMATTER_CONFIG]
;log-unknown-invalid: True
;unknown-destination: logs/unknown.log
;invalid-destination: logs/invalid.log

[VIEWING_CONFIG]
serialize-filepath: /tmp/viewing_table.data
saving-filepath: /var/log/ids.json
#serialize-live-filepath: /tmp/viewing_live_table.pickle
#serialize-vod-filepath: /tmp/viewing_vod_table.pickle

[SESSION_CONFIG]
serialize-filepath: /tmp/session_table.data
saving-filepath: /var/log/ids.json
#serialize-live-filepath: /tmp/session_live_table.pickle
#serialize-vod-filepath: /tmp/session_vod_table.pickle

[PYKAFKA_PRODUCER_CONFIG]
type: pykafka
log-serializer: default
host: ************:9092
;host: kafka-server:9092
use-rdkafka: True
compression: none
client-id: log.filter.producer
nginx-log-topic-name: all-filtered-nginx-log-entries
wowza-log-topic-name: all-filtered-wowza-log-entries
rtmp-log-topic-name: all-filtered-rtmp-log-entries
live-viewing-topic-name: all-filtered-live-viewing-entries
vod-viewing-topic-name: all-filtered-vod-viewing-entries
# Producer queue settings to prevent ProducerQueueFullError
max-queued-messages: 50000

;[PYKAFKA_PRODUCER_CONFIG]
;type: pykafka
;log-serializer: default
;host: **********:9092
;use-rdkafka: True
;client-id: log.filter.producer.test
;nginx-log-topic-name: all-filtered-nginx-log-entries
;wowza-log-topic-name: all-filtered-wowza-log-entries
;live-viewing-topic-name: all-filtered-live-viewing-entries
;vod-viewing-topic-name: all-filtered-vod-viewing-entries

;[FILE_PRODUCER_CONFIG]
;type: file
;log-serializer: default
;output-dir: /ssd/log/ingest
;max-file-size: 1048576000
;nginx-log-topic-name: all-filtered-nginx-log-entries
;wowza-log-topic-name: all-filtered-wowza-log-entries
;live-viewing-topic-name: all-filtered-live-viewing-entries
;vod-viewing-topic-name: all-filtered-vod-viewing-entries

[FILE_PRODUCER_CONFIG]
type: file
log-serializer: default
zip-files: False
output-dir: /usr/local/share/udger/database/testfile/
max-file-size: 1048576000
nginx-log-topic-name: all-filtered-nginx-log-entries
rtmp-log-topic-name: all-filtered-rtmp-log-entries
wowza-log-topic-name: all-filtered-wowza-log-entries
live-viewing-topic-name: all-filtered-live-viewing-entries
vod-viewing-topic-name: all-filtered-vod-viewing-entries

[UAS_PARSER3_AGENTS_USER_AGENT_CONFIG]
type: uas-parser3
server: True
alias: user-agent
host: localhost
port: 18812
ini-url: file:///usr/local/share/uasparser3/database/udgerdata_old.ini

[LOCAL_MAILER_CONFIG]
host = mailer-server
from-display-name = Log filter mailer
from-username = log-filter
to-display-name = Log filter developer
to-username = <EMAIL>
