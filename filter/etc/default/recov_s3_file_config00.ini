[COMMON_CONFIG]
type: archive-log
debug: False
serialize-tables: False
[LOG_FILTER_CONFIG]
consumer: S3_FILE_CONSUMER_CONFIG
#consumer: LOG_FILE_CONSUMER_CONFIG
formatter: FORMATTER_CONFIG
viewing: VIEWING_CONFIG
session: SESSION_CONFIG
producer: FILE_PRODUCER_CONFIG
user-agent: UAS_PARSER3_AGENTS_USER_AGENT_CONFIG
mailer-config = LOCAL_MAILER_CONFIG

[S3_FILE_CONSUMER_CONFIG]
#[LOG_FILE_CONSUMER_CONFIG]
#type: log-file
syslog-tag: nginx
#input-file: .*\.log
#input-dir: /usr/local/share/uasparser3/database/20171024/10/logs-rsyslog/
type: s3-file
s3-bucket = logs-raw.hexaglobe.com
s3-base-key = PROD
config-file: /etc/filter/.s3cfg
temp-dir: /var/log/recov
start-date: 2020-01-01:01:00:00
stop-date: 2020-01-01:02:00:00
server-name: logs-rsyslog
file-regex: .*\.log

[FORMATTER_CONFIG]
log-active: False
log-unknown-service: False
log-unknown: False
log-invalid: False
unknown-service-destination: /var/log/recov/logs/unknown-service.log
unknown-destination: /var/log/recov/logs/unknown.log
invalid-destination: /var/log/recov/logs/invalid.log

[VIEWING_CONFIG]
serialize-live-filepath: /var/log/recov/viewing_live_table.pickle
serialize-vod-filepath: /var/log/recov/viewing_vod_table.pickle
saving-filepath: /var/log/ids.json

[SESSION_CONFIG]
serialize-live-filepath: /var/log/recov/session_live_table.pickle
serialize-vod-filepath: /var/log/recov/session_vod_table.pickle
saving-filepath: /var/log/ids.json

[FILE_PRODUCER_CONFIG]
type: file
log-serializer: default
zip-files: False
output-dir: /usr/local/share/udger/database/20200101/00
max-file-size: 1048576000
nginx-log-topic-name: all-filtered-nginx-log-entries
rtmp-log-topic-name: all-filtered-rtmp-log-entries
wowza-log-topic-name: all-filtered-wowza-log-entries
live-viewing-topic-name: all-filtered-live-viewing-entries
vod-viewing-topic-name: all-filtered-vod-viewing-entries

[UAS_PARSER3_AGENTS_USER_AGENT_CONFIG]
type: uas-parser3
server: True
alias: user-agent
host: localhost
port: 18812
ini-url: file:///usr/local/share/uasparser3/database/udgerdata_old.ini

[LOCAL_MAILER_CONFIG]
host = mailer-server
from-display-name = Log filter mailer
from-username = log-filter
to-display-name = Log filter developer
to-username = <EMAIL>