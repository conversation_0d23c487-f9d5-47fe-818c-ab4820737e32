[COMMON_CONFIG]
type: update-log
debug: False
serialize-tables: False
[LOG_FILTER_CONFIG]
consumer: PYKAFKA_SIMPLE_CONSUMER_CONFIG
formatter: FORMATTER_CONFIG
viewing: VIEWING_CONFIG
session: SESSION_CONFIG
producer: P<PERSON><PERSON><PERSON><PERSON>_PRODUCER_CONFIG
user-agent: UAS_PARSER3_AGENTS_USER_AGENT_CONFIG
mailer-config = LOCAL_MAILER_CONFIG
[PYKAFKA_SIMPLE_CONSUMER_CONFIG]
type: pykafka
log-ingestion: False
kafka-type: simple-consumer
topic: all-service-entries-1
consumer-group: log.filter.consumer
server: localhost:9092
use-rdkafka: True
fetch-min-bytes: 1024
fetch-message-max-bytes: 2097152
auto-commit-enable: True
auto-commit-interval-ms: 15000
auto-offset-reset: earliest
num-consumer-fetchers: 1
queued-max-messages: 5000
[FORMATTER_CONFIG]
log-active: False
log-unknown-service: False
log-unknown: False
log-invalid: False
unknown-service-destination: /var/log/unknown-service.log
unknown-destination: /var/log/unknown.log
invalid-destination: /var/log/invalid.log
[VIEWING_CONFIG]
serialize-filepath: /tmp/viewing_table.data
saving-filepath: /var/log/ids.json
saving-offset: 1
saving-period: 3
[SESSION_CONFIG]
serialize-filepath: /tmp/session_table.data
saving-filepath: /var/log/ids.json
saving-offset: 1
saving-period: 3
[PYKAFKA_PRODUCER_CONFIG]
type: pykafka
log-serializer: default
host: localhost:9092
use-rdkafka: True
compression: snappy
client-id: log.filter.producer
nginx-log-topic-name: all-filtered-nginx-log-entries
wowza-log-topic-name: all-filtered-wowza-log-entries
rtmp-log-topic-name: all-filtered-rtmp-log-entries
live-viewing-topic-name: all-filtered-live-viewing-entries
vod-viewing-topic-name: all-filtered-vod-viewing-entries
[NONE_USER_AGENT_CONFIG]
type: none
[UAS_PARSER3_AGENTS_USER_AGENT_CONFIG]
type: uas-parser3
server: True
alias: user-agent
host: localhost
port: 18812
ini-url: file:///usr/local/share/uasparser3/database/udgerdata_old.ini
[LOCAL_MAILER_CONFIG]
host = mailer-server
from-display-name = Log filter mailer
from-username = log-filter
to-display-name = Log filter developer
to-username = <EMAIL>
