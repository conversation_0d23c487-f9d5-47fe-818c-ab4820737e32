#! /bin/sh -e
BASE=$(dirname "$0")
cd "$BASE"
exit 0
# Setup compiler.
apt-get install --without-recommends gcc g++ musl-dev python3 python3-dev zlib1g zlib1g-dev python3-snappy libsnappy-dev python3-setuptools librdkafka1 librdkafka-dev python3-ujson python3-kafka python3-rpyc python3-pip

pip3 install --upgrade pip setuptools

# Install uasparser3.
python3 setup.py install

# Install python packages (after librdkafka).
pip3 install pykafka

# Clean compiler.
