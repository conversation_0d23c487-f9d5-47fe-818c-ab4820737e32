FROM ubuntu:18.04


ARG HOST_GID
ARG HOST_UID

LABEL maintainer="<PERSON> <<EMAIL>>"

ENV TZ=Europe/Paris
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENV DEBIAN_FRONTEND=noninteractive

# Update package list and install required packages with specific versions
RUN apt-get update && \
    apt-get install -y \
    g++=4:7.4.0-1ubuntu2.3 \
    g++-7=7.5.0-3ubuntu1~18.04 \
    gcc=4:7.4.0-1ubuntu2.3 \
    gcc-7=7.5.0-3ubuntu1~18.04 \
    gcc-7-base=7.5.0-3ubuntu1~18.04 \
    gcc-8-base=8.4.0-1ubuntu1~18.04 \
    libgcc-7-dev=7.5.0-3ubuntu1~18.04 \
    libgcc1=1:8.4.0-1ubuntu1~18.04 \
    libpython3-dev=3.6.7-1~18.04 \
    libpython3-stdlib=3.6.7-1~18.04 \
    libpython3.6=3.6.9-1~18.04ubuntu1.12 \
    libpython3.6-dev=3.6.9-1~18.04ubuntu1.12 \
    libpython3.6-minimal=3.6.9-1~18.04ubuntu1.12 \
    libpython3.6-stdlib=3.6.9-1~18.04ubuntu1.12 \
    librdkafka-dev=0.11.3-1build1 \
    librdkafka1=0.11.3-1build1 \
    libsnappy-dev=1.1.7-1 \
    musl-dev=1.1.19-1 \
    python3=3.6.7-1~18.04 \
    python3-apt=1.6.6 \
    python3-dateutil=2.6.1-1 \
    python3-dbus=1.2.6-1 \
    python3-dev=3.6.7-1~18.04 \
    python3-distutils=3.6.9-1~18.04 \
    python3-gi=3.26.1-2ubuntu1 \
    python3-kafka=1.3.2-0ubuntu1 \
    python3-lib2to3=3.6.9-1~18.04 \
    python3-magic=2:0.4.15-1 \
    python3-minimal=3.6.7-1~18.04 \
    python3-pip=9.0.1-2.3~ubuntu1.18.04.8 \
    python3-pkg-resources=39.0.1-2ubuntu0.1 \
    python3-rpyc=3.4.4-1 \
    python3-setuptools=39.0.1-2ubuntu0.1 \
    python3-six=1.11.0-2 \
    python3-snappy=0.5-1.1build2 \
    python3-software-properties=**********.22 \
    python3-ujson=1.35-2 \
    python3.6=3.6.9-1~18.04ubuntu1.12 \
    python3.6-dev=3.6.9-1~18.04ubuntu1.12 \
    python3.6-minimal=3.6.9-1~18.04ubuntu1.12 \
    sudo=1.8.21p2-3ubuntu1.6 \
    tzdata=2023c-0ubuntu0.18.04 \
    zlib1g=1:1.2.11.dfsg-0ubuntu2.2 \
    zlib1g-dev=1:1.2.11.dfsg-0ubuntu2.2 \
    python3-venv && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN python3 -m pip install --upgrade pip setuptools
RUN mkdir /var/status
RUN mkdir /var/status/data
RUN chmod 777 /var/status/data
# Install uasparser3.

# Install python packages (after librdkafka).
RUN python3 -m pip install pykafka udger confluent-kafka==2.4.0 avro python-geoip requests geoip2 itsdangerous pandas redis expiringdict regex
#RUN rm -rf /tmp/*

ENV FILTER_LOG_DIR=/var/log \
    FILTER_CFG_DIR=/etc/filter

WORKDIR /opt/filter

RUN groupadd filter -g "$HOST_GID" && useradd -g filter -s /bin/sh -u "$HOST_UID" filter
#RUN getent group filter &>/dev/null || ( \
#    groupadd filter -g "$HOST_GID" && \
#    useradd -g filter -s /bin/sh -u "$HOST_UID" filter)


VOLUME /var/log \
       /opt/filter \
       /etc/filter \
       /usr/local/bin \
       /usr/local/share/udger/database/

CMD python3 -u ./run.py /etc/filter/test_archive_file_config00.ini --modulo_result $SCALE_NB
