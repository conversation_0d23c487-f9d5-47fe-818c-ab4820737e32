#!/bin/bash -e
display_usage(){
    echo -e "Usage: $0 <install|update|delete>"
}
if [ $# -ge 2 ]
then
    display_usage
    exit 1
fi
if [ $# -ge 1 ]
then
    OPTION="$1"
else
    OPTION='install'
fi
BASE=$(dirname "$0")
SRC_DIR=$(cd "$BASE"; pwd)/src/uasparser3
case $OPTION in
install)
    mkdir -p "$SRC_DIR"
    git clone ssh://*****************************/diffusion/LOGUAS/uasparser3.git "$SRC_DIR"
    # shellcheck source=./.env
    source "$BASE/../.env"
    if [ ! -f "${UA_DATABASE_DIR}/udgerdata_old.ini" ]
    then
        "$SRC_DIR/untar_database.sh"
        mv "$SRC_DIR/database/udgerdata_old.ini" "${UA_DATABASE_DIR}/udgerdata_old.ini"
        rm -rf "$SRC_DIR/database"
    fi
    ;;
update)
    cd "$SRC_DIR"
    git pull
    ;;
delete)
    echo "$SRC_DIR"
    rm -rI "$SRC_DIR"
    ;;
*)
    display_usage
    exit 1
    ;;
esac
